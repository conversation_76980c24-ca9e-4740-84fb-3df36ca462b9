<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON> - AI Portfolio</title>
  <meta name="description" content="<PERSON><PERSON><PERSON> Gupta AI Portfolio" />
  <meta name="author" content="<PERSON>ks<PERSON>" />

  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <!-- Google Fonts -->

  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet" />

  <!-- Additional CSS Files -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/templatemo-digimedia-v1.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animated.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animate.min.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/owl.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/contact.css') }}" />

  <!-- Custom Inline Styles for SPLASH ANIMATION, Notification Banner, and Responsive Navbar -->
  <style>
    :root {
      --circle-img-size: calc(3vw + 70px);
    }

    /* Floating Chat Button remains unchanged */
    .floating-chat-btn {
      position: fixed;
      bottom: 60px;
      right: 30px;
      z-index: 1060;
    }
    .notification-banner {
      width: 100%;
      background: linear-gradient(90deg, #1f1f1f, #3c3c3c);
      padding: 10px 0;
      overflow: hidden;
      white-space: nowrap;
      border-bottom: 2px solid #444;
      /* Positioned in normal document flow so it appears only above the main banner */
    }
    .notification-text {
  display: inline-block;
  color: #fff;  /* Plain white text */
  font-size: 1.2rem;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 500;
  /* Removed gradient and text-fill properties for a normal font style */
  text-shadow: 1px 1px 2px rgba(0,0,0,0.6); /* Retain subtle shadow for legibility */
  animation: marquee 15s linear infinite;
}

@keyframes marquee {
  0%   { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

    /* --------------------------------------------------------------------
       SPLASH ANIMATION OVERLAY
       A modern, semi-transparent gradient background with a rotating circle of images.
    --------------------------------------------------------------------- */
    #splash {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      background: linear-gradient(135deg, rgba(102,126,234,0.7), rgba(118,75,162,0.7));
      backdrop-filter: blur(5px);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      overflow: hidden;
    }
    .circle-container {
      position: relative;
      width: calc(30vw + 200px);
      height: calc(30vw + 200px);
      border-radius: 50%;
      animation: rotateCircle 5s linear forwards;
    }
    @keyframes rotateCircle {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .circle-image {
      position: absolute;
      width: var(--circle-img-size);
      height: var(--circle-img-size);
      border-radius: 50%;
      border: none;
      box-shadow: none;
      transition: transform 1s ease, top 1s ease, left 1s ease;
    }
    /* --------------------------------------------------------------------
       INNER ALERT CIRCLE
       A smaller circular element in the center with dynamic alert text.
    --------------------------------------------------------------------- */
    .inner-alert {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: calc((30vw + 200px) * 0.25);
      height: calc((30vw + 200px) * 0.25);
      border-radius: 50%;
      background-color: #ff4444;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: clamp(0.8rem, 2vw, 1.2rem);
      font-family: 'Poppins', sans-serif;
      text-align: center;
      padding: 5px;
      word-break: break-word;
      line-height: 1.2;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      z-index: 10;
    }

    /* --------------------------------------------------------------------
       PROFILE IMAGE STYLES for Main Banner & About Section
       (Fixed dimensions without rounding)
    --------------------------------------------------------------------- */
    #mainProfilePic,
    #aboutProfilePic {
      width: 100%;
      height: 360;
      border-radius: 10px;
      object-fit: cover;
      border: none;
      box-shadow: none;
      display: block;
      margin: 0 auto;
    }

    /* --------------------------------------------------------------------
       (Optional) Notification Banner - if you want one at the top of your content
    --------------------------------------------------------------------- */
    .notification-banner {
      width: 100%;
      background: linear-gradient(90deg, #1f1f1f, #3c3c3c);
      padding: 10px 0;
      overflow: hidden;
      white-space: nowrap;
      border-bottom: 2px solid #444;
    }
    .notification-text {
  display: inline-block;
  font-size: 1.2rem;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 600;
  background: linear-gradient(45deg, #ffffff, #dddddd); /* Brighter gradient for higher contrast */
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); /* Increased shadow for better legibility */
  animation: marquee 15s linear infinite;
}

@keyframes marquee {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}
  </style>
</head>
<body>
  <!-- (Optional) Notification Banner (if you want it visible only above the main banner) -->
  <!-- Uncomment the block below if needed -->
  <!--
  <div class="notification-banner">
    <div class="notification-text">
      None of image is real, all are generated using Custom LORA model &bull;
    </div>
  </div>
  -->

  <!-- SPLASH ANIMATION OVERLAY -->
  <div id="splash">
    <div class="circle-container" id="circleContainer">
      <!-- Rotating circle images will be injected here by JavaScript -->
    </div>
    <div class="inner-alert">
      My Portfolio
    </div>
  </div>

  <!-- ***** Header / Navbar ***** -->
  <header>
    <!-- Use navbar-dark to ensure proper toggler appearance on dark backgrounds -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
      <div class="container">
        <a class="navbar-brand" href="#top">
          <span class="logo-text">Akshat Gupta</span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item"><a class="nav-link" href="#top">Home</a></li>
            <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
            <li class="nav-item"><a class="nav-link" href="#academics">Acadamics</a></li>
            <li class="nav-item"><a class="nav-link" href="#research">Research</a></li>
            <li class="nav-item"><a class="nav-link" href="#skills">Skills</a></li>
            <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
            <li class="nav-item"><a class="nav-link" href="#certificates">Certificates</a></li>
            <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('blog') }}">Blog</a>
            </li>
            <li class="nav-item">
              <a class="nav-link download-resume" href="{{ url_for('static', filename='Resume_AkshatGupta.pdf') }}" download>
                Download Resume
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>
  <!-- <div class="notification-banner">
    <div class="notification-text">
      None of image is real, all are generated using Custom LORA model &bull; Use the Floating AI chatbot to get more information about me and my projects&bull;
    </div> -->
  </div>


  <!-- ***** Main Banner Area ***** -->
  <div id="top" class="main-banner">
    <div class="container">
      <div class="row align-items-center">
        <!-- Text Section (Left Side) -->
        <div class="col-lg-6">
          <h6 class="wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s"
              style="text-transform: uppercase; color: #b42c1d; font-size: 22px; font-weight: 700; letter-spacing: 2px;">
            Welcome to My Portfolio
          </h6>
          <h2>
            <span>I'm</span>
            <span id="animated-name">
              <span>Akshat Gupta</span>
              <span>अक्षत गुप्ता</span>
            </span>
          </h2>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
            <span style="color: #b42c1d; font-weight: bold;">AI & ML Enthusiast</span> | Innovator
          </p>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
            Explore my portfolio to see the latest projects where I applied AI to drive real-world impact and bring innovation to various domains.
          </p>

          <!-- Skill Highlights -->
          <div class="banner-skills wow fadeInLeft" data-wow-duration="1s" data-wow-delay="1.1s">
            <span class="banner-skill-badge">AI</span>
            <span class="banner-skill-badge">ML</span>
            <span class="banner-skill-badge">Deep Learning</span>
            <span class="banner-skill-badge">NLP</span>
          </div>

          <a href="#projects" class="btn btn-outline-light wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.4s">
            View my work
          </a>
        </div>
        <!-- Image Section (Right Side) -->
        <div class="col-lg-6">
          <img id="mainProfilePic" src="" alt="Profile Image" class="banner-image">
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Main Banner ***** -->
<!-- ***** About Section ***** -->
<section id="about" class="about section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        About Me
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        AI Enthusiast • Innovator • Lifelong Learner
      </p>
    </div>
    <div class="about-card-wrapper wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.7s">
      <div class="about-card">
        <div class="about-image">
          <img id="aboutProfilePic" src="" alt="Profile Image" class="img-fluid">
        </div>
        <div class="about-info">
          <h3>Welcome to my journey</h3>
          <p>
            I am an enthusiastic AI student dedicated to artificial intelligence, machine learning, deep learning, and generative AI. Currently pursuing a B.Tech in Computer Science & Engineering at Bennett University, I develop innovative solutions that combine technical expertise with creative thinking. I constantly explore emerging trends, dive into new projects, and nurture my hobbies, all of which fuel my passion for advancing AI technology.
          </p>
          <a href="#contact" class="btn about-cta">Let’s Connect</a>
          <button class="btn about-cta" data-bs-toggle="modal" data-bs-target="#resumeModal">
            View My Resume
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Resume Modal -->

<!-- Resume Modal -->
<div class="modal fade" id="resumeModal" tabindex="-1" aria-labelledby="resumeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="resumeModalLabel">My Resume</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Replace 'images/resume.png' with the path to your resume image -->
        <img src="{{ url_for('static', filename='images/Resume.jpg') }}" alt="Resume" class="img-fluid">
      </div>
      <div class="modal-footer">
        <!-- Download Resume Button -->
        <a href="{{ url_for('static', filename='Resume_AkshatGupta.pdf') }}" download class="btn about-cta">
          Download Resume
        </a>
        <button type="button" class="btn about-cta" data-bs-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
  <!-- ***** End About Section ***** -->


  <!-- ***** Academic Highlights Section ***** -->
<!-- Academic Highlights Section -->
<section id="academics" class="academics section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Academic Highlights
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        A showcase of my academic performance.
      </p>
    </div>

    <!-- Background Decorative Waves Removed for Consistency -->

    <div class="row g-4">
      <!-- CGPA Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <div class="academic-icon"><i class="fas fa-graduation-cap"></i></div>
          <h4>Current CGPA</h4>
          <p>8.64 / 10</p>
        </div>
      </div>

      <!-- University Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          <div class="academic-icon"><i class="fas fa-university"></i></div>
          <h4>Bennett University</h4>
          <p>B.Tech, Computer Science & Engineering (2022 - 2026)</p>
        </div>
      </div>

      <!-- Higher Secondary Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <div class="academic-icon"><i class="fas fa-school"></i></div>
          <h4>Higher Secondary</h4>
          <p>80% (CBSE, 2022)</p>
        </div>
      </div>

      <!-- Senior Secondary Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
          <div class="academic-icon"><i class="fas fa-book"></i></div>
          <h4>Senior Secondary</h4>
          <p>95.2% (CBSE, 2020)</p>
        </div>
      </div>

      <!-- Selected Courses Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.1s">
          <div class="academic-icon"><i class="fas fa-book-open"></i></div>
          <h4>Selected Courses</h4>
          <p>
            Machine Learning<br>
            Artificial Intelligence<br>
            Data Structures & Algorithms<br>
            Probability & Statistics
          </p>
        </div>
      </div>

      <!-- Academic Interests Card -->
      <div class="col-lg-4 col-md-6">
        <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.3s">
          <div class="academic-icon"><i class="fas fa-lightbulb"></i></div>
          <h4>Academic Interests</h4>
          <p>
            Exploring cutting-edge AI techniques, deep learning, and computer vision while staying updated on industry trends.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

  <!-- ***** Skills Section ***** -->
<!-- Skills Section -->
<section id="skills" class="skills section">
  <div class="container">
      <div class="text-center mb-5">
        <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
          Technical Skills
        </h2>
        <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
          My expertise in AI, deep learning, generative models, and software development.
        </p>
      </div>

      <div class="row g-4 justify-content-center">
          <!-- Machine Learning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/3066/3066305.png" alt="Machine Learning">
                  </div>
                  <h4>Machine Learning</h4>
                  <p>Supervised & Unsupervised Learning, Classification, Regression</p>
              </div>
          </div>

          <!-- Deep Learning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/3242/3242257.png" alt="Deep Learning">
                  </div>
                  <h4>Deep Learning</h4>
                  <p>Neural Networks, CNNs, RNNs, LSTMs, GANs</p>
              </div>
          </div>

          <!-- Generative AI & Fine-Tuning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/7017/7017303.png" alt="Generative AI">
                  </div>
                  <h4>Generative AI & Fine-Tuning</h4>
                  <p>
                      Hugging Face, LoRA Models, OpenAI API, Fine-tuning GPT, Stable Diffusion
                  </p>
              </div>
          </div>

          <!-- NLP & LLMs -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/1730/1730153.png" alt="NLP & LLMs">
                  </div>
                  <h4>NLP & Large Language Models</h4>
                  <p>Transformers, BERT, GPT, RAG, LangChain</p>
              </div>
          </div>

          <!-- Computer Vision -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.1s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/1680/1680292.png" alt="Computer Vision">
                  </div>
                  <h4>Computer Vision</h4>
                  <p>OpenCV, YOLO, Image Segmentation, Object Detection</p>
              </div>
          </div>

          <!-- Cloud & APIs -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.3s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/2329/2329035.png" alt="Cloud & APIs">
                  </div>
                  <h4>Cloud & APIs</h4>
                  <p>Google Cloud, AWS, OpenAI API, Hugging Face API</p>
              </div>
          </div>
      </div>
  </div>
</section>
  <!-- ***** End Skills Section ***** -->

<!-- ***** Projects Section ***** -->

<div id="projects" class="projects section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        My Projects
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        Here are some of my featured projects that showcase my skills and expertise.
      </p>
    </div>

    <!-- Desktop Grid Layout (Visible on large screens) -->
    <div id="projects-grid" class="row d-none d-lg-flex">
      <!-- Project 1 -->
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <img src="{{ url_for('static', filename='images/Akshar.png') }}" alt="Project 1" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Akshar</h4>
            <p>A chat app that translates text and audio messages in real time, built using Flutter.</p>
            <div class="tech-stack">
              <span class="badge">Flutter</span>
              <span class="badge">Dart</span>
              <span class="badge">AI</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <!-- Replace with your LoRA model image -->
            <img src="{{ url_for('static', filename='images/829342611589419847.png') }}" alt="LoRA Model" class="project-image img-fluid">
            <div class="project-overlay">
                <h4>Fine-Tuned LoRA Model</h4>
                <p>This custom LoRA model has been fine-tuned with my own face, and trained on Flux models as the base. It now has the ability to generate realistic, high-quality images of various types. From generating lifelike portraits to creative designs, this model reflects the power of generative AI and deep learning.</p>
                <div class="tech-stack">
                    <span class="badge">LoRA</span>
                    <span class="badge">Generative AI</span>
                    <span class="badge">Flux Models</span>
                </div>
                <!-- No GitHub link here -->
            </div>
        </div>
    </div>
      <!-- Project 2 -->
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          <img src="{{ url_for('static', filename='images/eye_detection.png') }}" alt="Project 2" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Eye Disease Detection</h4>
            <p>Empowering precise eye disease detection through cutting-edge generative AI and deep learning on fundus images.</p>
            <div class="tech-stack">
              <span class="badge">Deep Learning</span>
              <span class="badge">Generative AI</span>
              <span class="badge">Python</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <!-- Project 3 -->
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <img src="{{ url_for('static', filename='images/medical_chatbot.png') }}" alt="Project 3" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Medical ChatBot</h4>
            <p>Medical ChatBot is a context-aware medical assistant that leverages retrieval-augmented generation with a locally stored vector database (built from PDFs) to deliver dynamic, AI-powered medical advice through a modern, conversational interface.</p>
            <div class="tech-stack">
              <span class="badge">RAG</span>
              <span class="badge">Python</span>
              <span class="badge">LangChain</span>
              <span class="badge">Generative AI</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/Medical-ChatBOT" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <!-- Project 4 -->
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/FacePay.png') }}" alt="Project 4" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>FacePay</h4>
            <p>An AI-based payment system using facial recognition for secure UPI transactions.</p>
            <div class="tech-stack">
              <span class="badge">DeepFace</span>
              <span class="badge">Flask</span>
              <span class="badge">SQLite3</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <!-- Project 5 -->
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/krishi_sanrakshan.jpg') }}" alt="Project 5" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Krishi Sanrakshan</h4>
            <p>Deep learning based model which works on detecting disease in livestock and plants.</p>
            <div class="tech-stack">
              <span class="badge">Deep Learning</span>
              <span class="badge">Python</span>
              <span class="badge">Streamlit</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/translation.png') }}" alt="Project 4" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>English to Hindi Text translation</h4>
            <p>Build an english to hindi translation app by implementing tranformers from scratch </p>
            <div class="tech-stack">
              <span class="badge">Python</span>
              <span class="badge">NLP</span>
              <span class="badge">Deep Learning</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/English-to-Hindi-Tranlstion-using-Transformers-Scratch-.git" class="btn btn-outline-light btn-sm">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/sentiment.jpg') }}" alt="Teacher-Student Distillation on IMDB" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Teacher-Student Distillation on IMDB</h4>
            <p>A sentiment analysis pipeline that distills a large teacher model into a compact student model for robust sentiment prediction on IMDB movie reviews.</p>
            <div class="tech-stack">
              <span class="badge">Teacher-Student Distillation</span>
              <span class="badge">Hugging Face</span>
              <span class="badge">Transformers</span>
              <span class="badge">IMDB</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/Teacher-Student-Distillation-on-IMDB-Movie-Sentiment-Reviews.git" class="btn btn-outline-light btn-sm" target="_blank">
              View on GitHub
            </a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/vision.png') }}" alt="Vision Transformer for MNIST" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Vision Transformer for MNIST</h4>
            <p>This repository contains an implementation of a Vision Transformer (ViT) built from scratch for image classification on the MNIST dataset. It features a custom encoder layer with multi-head self-attention and a feed-forward network.</p>
            <div class="tech-stack">
              <span class="badge">Vision Transformer</span>
              <span class="badge">PyTorch</span>
              <span class="badge">MNIST</span>
              <span class="badge">Attention</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/Vision-Transformers-from-scratch.git" class="btn btn-outline-light btn-sm" target="_blank">
              View on GitHub
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-4 project-item">
        <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <img src="{{ url_for('static', filename='images/custom_emotion.png') }}" alt="Custom Emotion Generator" class="project-image img-fluid">
          <div class="project-overlay">
            <h4>Custom Emotion Generator</h4>
            <p>A powerful tool that can animate expressive facial expressions from a single reference image, allowing users to create various emotional expressions by manipulating facial features through an intuitive interface powered by advanced AI techniques.</p>
            <div class="tech-stack">
              <span class="badge">PyTorch</span>
              <span class="badge">Computer Vision</span>
              <span class="badge">CUDA</span>
              <span class="badge">Gradio</span>
            </div>
            <a href="https://github.com/Akshat-Gupta04/Custom-Emotion-Generator.git" class="btn btn-outline-light btn-sm" target="_blank">
              View on GitHub
            </a>
          </div>
        </div>
      </div>

    </div>
    <!-- End Desktop Grid Layout -->

    <!-- Modern Mobile Projects Slider (Visible only on screens smaller than large) -->
    <div class="mobile-projects-slider d-lg-none">
      <div class="slider-container" id="projectsSlider">
        <!-- Project 1 Slide -->
        <div class="slider-slide" data-index="0">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <img src="{{ url_for('static', filename='images/Akshar.png') }}" alt="Project 1" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Akshar</h4>
              <p>A chat app that translates text and audio messages in real time, built using Flutter.</p>
              <div class="tech-stack">
                <span class="badge">Flutter</span>
                <span class="badge">Dart</span>
                <span class="badge">AI</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 2 Slide -->
        <div class="slider-slide" data-index="1">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <img src="{{ url_for('static', filename='images/829342611589419847.png') }}" alt="lora_model" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Fine-Tuned LoRA Model</h4>
              <p>This custom LoRA model has been fine-tuned with my own face, and trained on Flux models as the base.</p>
              <div class="tech-stack">
                <span class="badge">LoRA</span>
                <span class="badge">Generative AI</span>
                <span class="badge">Flux Models</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 3 Slide -->
        <div class="slider-slide" data-index="2">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <img src="{{ url_for('static', filename='images/eye_detection.png') }}" alt="Project 2" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Eye Disease Detection</h4>
              <p>Empowering precise eye disease detection through cutting-edge generative AI and deep learning on fundus images.</p>
              <div class="tech-stack">
                <span class="badge">Deep Learning</span>
                <span class="badge">Generative AI</span>
                <span class="badge">Python</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 4 Slide -->
        <div class="slider-slide" data-index="3">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <img src="{{ url_for('static', filename='images/medical_chatbot.png') }}" alt="Project 3" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Medical ChatBot</h4>
              <p>Medical ChatBot is a context-aware medical assistant that leverages retrieval-augmented generation.</p>
              <div class="tech-stack">
                <span class="badge">RAG</span>
                <span class="badge">Python</span>
                <span class="badge">LangChain</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/Medical-ChatBOT" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 5 Slide -->
        <div class="slider-slide" data-index="4">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <img src="{{ url_for('static', filename='images/FacePay.png') }}" alt="Project 4" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>FacePay</h4>
              <p>An AI-based payment system using facial recognition for secure UPI transactions.</p>
              <div class="tech-stack">
                <span class="badge">DeepFace</span>
                <span class="badge">Flask</span>
                <span class="badge">SQLite3</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 6 Slide -->
        <div class="slider-slide" data-index="5">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <img src="{{ url_for('static', filename='images/krishi_sanrakshan.jpg') }}" alt="Project 5" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Krishi Sanrakshan</h4>
              <p>Deep learning based model which works on detecting disease in livestock and plants.</p>
              <div class="tech-stack">
                <span class="badge">Deep Learning</span>
                <span class="badge">Python</span>
                <span class="badge">Streamlit</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 7 Slide -->
        <div class="slider-slide" data-index="6">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <img src="{{ url_for('static', filename='images/translation.png') }}" alt="lora_model" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>English to Hindi Translation</h4>
              <p>Build an english to hindi translation app by implementing tranformers from scratch.</p>
              <div class="tech-stack">
                <span class="badge">Python</span>
                <span class="badge">NLP</span>
                <span class="badge">Deep Learning</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 8 Slide -->
        <div class="slider-slide" data-index="7">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <img src="{{ url_for('static', filename='images/sentiment.jpg') }}" alt="teacher_student" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Teacher-Student Distillation</h4>
              <p>A sentiment analysis pipeline that distills a large teacher model into a compact student model.</p>
              <div class="tech-stack">
                <span class="badge">Distillation</span>
                <span class="badge">Hugging Face</span>
                <span class="badge">Transformers</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/Teacher-Student-Distillation-on-IMDB-Movie-Sentiment-Reviews.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 9 Slide -->
        <div class="slider-slide" data-index="8">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <img src="{{ url_for('static', filename='images/vision.png') }}" alt="Vision Transformer for MNIST" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Vision Transformer for MNIST</h4>
              <p>A Vision Transformer (ViT) built from scratch for image classification on the MNIST dataset.</p>
              <div class="tech-stack">
                <span class="badge">Vision Transformer</span>
                <span class="badge">PyTorch</span>
                <span class="badge">MNIST</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/Vision-Transformers-from-scratch.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>

        <!-- Project 10 Slide -->
        <div class="slider-slide" data-index="9">
          <div class="project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <img src="{{ url_for('static', filename='images/custom_emotion.png') }}" alt="Custom Emotion Generator" class="project-image img-fluid">
            <div class="project-overlay">
              <h4>Custom Emotion Generator</h4>
              <p>A tool that can animate expressive facial expressions from a single reference image.</p>
              <div class="tech-stack">
                <span class="badge">PyTorch</span>
                <span class="badge">Computer Vision</span>
                <span class="badge">CUDA</span>
              </div>
              <a href="https://github.com/Akshat-Gupta04/Custom-Emotion-Generator.git" class="btn btn-outline-light btn-sm">
                View on GitHub
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Modern Controls -->
      <div class="slider-controls">
        <button class="slider-btn" id="prevBtn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="slider-btn" id="nextBtn">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>

      <!-- Slider Dots -->
      <div class="slider-dots" id="sliderDots"></div>
    </div>
    <!-- End Modern Mobile Slider Layout -->
  </div>
</div>
<!-- ***** End Projects Section ***** -->
 <!-- ***** Research Papers Section ***** -->
<section id="research" class="research section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Research Papers
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        Below are the research papers that I am currently working on.
      </p>
    </div>

    <div class="row g-4">
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          <h4>Multi-Modal Meta Learner (M3L): A Unified, Scalable Architecture for State-of-the-Art Image Processing</h4>
          <p><strong>Abstract:</strong>A state-of-the-art image processing model that unifies several advanced deep learning techniques into a single architecture. It integrates Sparse CNN, Vision Transformers, Graph Neural Networks, along with Diffusion Models, Neural Architecture Search, and Masked Autoencoders for self-supervised representation learning.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
           </div>
        </div>
      </div>
      <!-- Research Paper 1 -->
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <h4>A Lightweight Deep Learning Framework for Detecting Potato Disease Using the Grasshopper Optimization Algorithm</h4>
          <p><strong>Abstract:</strong>This study focused on artificial intelligence applied in potato disease detection,through a lightweight deep learning framework. The suggested model uses an integration of MobileNetV2 with Grasshopper Optimization Algorithm,for five potato diseases for classification.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
          </div>
        </div>
      </div>

      <!-- Research Paper 2 -->


      <!-- Research Paper 3 -->
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <h4>Deep Learning-Based Brain Tumor Identification</h4>

          <p><strong>Abstract:</strong> This research paper mainly focuses on using deep learning techniques, specifically convolutional neural networks (CNNs), to identify brain tumors from magnetic resonance imaging (MRI) scans. The chosen dataset contains scans of brain tumors consisting of 4 classes.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- ***** End Research Papers Section ***** -->
 <!-- Certificates Section -->
<!-- Certificates Section -->
<section id="certificates" class="certificates section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Certificates
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        Browse through my certifications.
      </p>
    </div>
    <!-- Bootstrap Carousel for Certificates (1 per slide) -->
    <div id="certificatesCarousel" class="carousel slide" data-bs-ride="carousel">
      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="4" aria-label="Slide 5"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="5" aria-label="Slide 6"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="6" aria-label="Slide 7"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="7" aria-label="Slide 8"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="8" aria-label="Slide 9"></button>
      </div>

      <div class="carousel-inner">
        <!-- Slide 1: Certificate 1 -->
        <div class="carousel-item active">
          <img src="{{ url_for('static', filename='images/cert1.png') }}" class="d-block w-100 certificate-img" alt="Certificate 1" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 2: Certificate 2 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert2.png') }}" class="d-block w-100 certificate-img" alt="Certificate 2" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 3: Certificate 3 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert3.png') }}" class="d-block w-100 certificate-img" alt="Certificate 3" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 4: Certificate 4 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert4.png') }}" class="d-block w-100 certificate-img" alt="Certificate 4" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 5: Certificate 5 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert5.png') }}" class="d-block w-100 certificate-img" alt="Certificate 5" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 6: Certificate 6 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert6.png') }}" class="d-block w-100 certificate-img" alt="Certificate 6" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 7: Certificate 7 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert7.png') }}" class="d-block w-100 certificate-img" alt="Certificate 7" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 8: Certificate 8 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert8.png') }}" class="d-block w-100 certificate-img" alt="Certificate 8" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 9: Certificate 9 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert9.png') }}" class="d-block w-100 certificate-img" alt="Certificate 9" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
      </div>
      <!-- Carousel Controls -->
      <button class="carousel-control-prev" type="button" data-bs-target="#certificatesCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#certificatesCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
      </button>
    </div>
  </div>
</section>

<!-- Certificate Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1" aria-labelledby="certificateModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="certificateModalLabel">Certificate</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <img src="" alt="Certificate" id="modalCertificateImg" class="img-fluid">
      </div>
    </div>
  </div>
</div>

<!-- Certificate Modal Script -->
<script>
  // Attach click event on all certificate images inside the carousel
  document.querySelectorAll('.certificate-img').forEach(function(img) {
    img.addEventListener('click', function() {
      var src = this.getAttribute('src');
      document.getElementById('modalCertificateImg').setAttribute('src', src);
      var modal = new bootstrap.Modal(document.getElementById('certificateModal'));
      modal.show();
    });
  });
</script>
  <!-- ***** Contact Section ***** -->
   <!-- Floating Blog Button -->
<!-- Floating Blog Button -->
<!-- <div class="floating-blog-btn">
  <a href="{{ url_for('blog') }}" class="btn btn-blog">
    <i class="fas fa-blog"></i> My Blogs
  </a>
</div> -->
 <!-- Contact Section -->
 <section id="contact" class="contact section">
  <div class="container">
    <h2 class="section-title wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">
      Get in Touch
    </h2>
    <p class="section-subtitle wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
      Have a project, question, or collaboration idea? Let’s connect and bring ideas to life.
    </p>

    <div class="contact-container">
      <div class="contact-info wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
        <h4>Contact Details</h4>
        <div class="contact-item">
          <!-- Replace 'assets/icons/email.svg' with your actual email SVG logo path -->
          <img src="static/images/mail.svg" alt="Email Logo" class="contact-icon">
          <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/linkedin.svg' with your actual LinkedIn SVG logo path -->
          <img src="static/images/linkedin.svg" alt="LinkedIn Logo" class="contact-icon">
          <a href="https://www.linkedin.com/in/akshat-gupta-077a7b256/" target="_blank">
            linkedin.com/in/akshat-gupta
          </a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/github.svg' with your actual GitHub SVG logo path -->
          <img src="static/images/github.svg" alt="GitHub Logo" class="contact-icon">
          <a href="https://github.com/Akshat-Gupta04" target="_blank">
            github.com/Akshat-Gupta04
          </a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/location.svg' with your actual Location SVG logo path -->

          <p>Greater Noida, India</p>
        </div>
      </div>
    </div>

    <p class="final-note wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
      Looking forward to connecting and exploring new opportunities.
    </p>
  </div>
</section>
  <!-- ***** End Contact Section ***** -->



  <!-- Floating Chat Button -->
  <div class="floating-chat-btn">
    <button id="chat-toggle" class="btn pulse-effect">
      <div class="particle-ring"></div>
      <img src="{{ url_for('static', filename='images/chat.svg') }}" alt="Chat" width="24" height="24">
    </button>
  </div>

  <!-- Floating Chat Window -->
  <div id="chat-window" class="floating-chat-window">
    <div class="chat-header">
      <div class="ai-status">
        <div class="ai-indicator"></div>
        <div class="ai-info">
          <span class="typing-text">AI Assistant</span>
          <span class="status">Online</span>
        </div>
      </div>
      <button id="close-chat" class="close-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18M6 6l12 12"/>
        </svg>
      </button>
    </div>

    <div class="chat-body" id="chat-body">
      <div class="welcome-message">
        <div class="ai-avatar-large">
          <div class="avatar-ring"></div>
          <img src="{{ url_for('static', filename='images/ai.jpg') }}" alt="AI Avatar">
        </div>
        <div class="welcome-text">
          <h3>Hello! I'm your AI assistant</h3>
          <p>I can help you learn more about Akshat's projects and experience.</p>
        </div>
      </div>
    </div>

    <div class="chat-input-wrapper">
      <div class="chat-input">
        <textarea id="user-input" placeholder="Ask me anything..." rows="1"></textarea>
        <button id="send-btn" class="send-btn-animated">
          <div class="send-btn-particle"></div>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
            <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
          </svg>
        </button>
      </div>
      <div class="input-features">
        <div class="feature-dots">
          <span></span><span></span><span></span>
        </div>
        <div class="input-suggestion">Try asking about specific projects</div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="container text-center">
      <p class="wow fadeIn" data-wow-duration="1s" data-wow-delay="0.3s">
        2025 © Akshat Gupta. All Rights Reserved.
      </p>
    </div>
  </footer>
  <!-- Scripts: Load each only once and in the proper order -->
  <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
  <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/owl-carousel.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animation.js') }}"></script>
  <script src="{{ url_for('static', filename='js/imagesloaded.js') }}"></script>
  <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animated-name.js') }}"></script>
  <script src="{{ url_for('static', filename='js/projects-slider.js') }}"></script>

  <!-- NEW SPLASH ANIMATION SCRIPT -->
  <script>
  // Track used images
  let usedImages = [];

  // Toggle chat window visibility
  document.getElementById("chat-toggle").addEventListener("click", function() {
    document.getElementById("chat-window").classList.toggle("show");
  });

  // Close chat window
  document.getElementById("close-chat").addEventListener("click", function() {
    document.getElementById("chat-window").classList.remove("show");
  });

  // Handle sending message
  document.getElementById("send-btn").addEventListener("click", async function() {
    var userInput = document.getElementById("user-input").value;
    if (userInput.trim() !== "") {
      addMessage(userInput, "user");
      document.getElementById("user-input").value = "";
      addMessage("Thinking...", "ai");
      try {
        const response = await fetch('/ask', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ question: userInput })
        });
        const data = await response.json();
        if (data.response) {
          updateAIResponse(data.response);
        } else {
          updateAIResponse("Sorry, I couldn't understand that.");
        }
      } catch (error) {
        updateAIResponse("Sorry, I'm having trouble connecting to the server.");
        console.error("Error:", error);
      }
    }
  });

  // Add message to the chat
  function addMessage(message, sender) {
    var chatBody = document.getElementById("chat-body");
    var messageElement = document.createElement("div");
    messageElement.classList.add("message", sender);
    if (sender === "user") {
      messageElement.innerHTML = `
        <div class="message-content">${escapeHtml(message)}</div>
        <img src="{{ url_for('static', filename='images/person.svg') }}" alt="User Avatar" class="avatar">
      `;
    } else if (sender === "ai") {
      messageElement.innerHTML = `
        <img src="{{ url_for('static', filename='images/ai.jpg') }}" alt="AI Avatar" class="avatar">
        <div class="message-content">${escapeHtml(message)}</div>
      `;
    }
    chatBody.appendChild(messageElement);
    chatBody.scrollTop = chatBody.scrollHeight;
  }

  // Update the last AI message with the response
  function updateAIResponse(message) {
    var chatBody = document.getElementById("chat-body");
    var aiMessageElement = chatBody.querySelector(".message.ai:last-child");
    if (aiMessageElement) {
      aiMessageElement.querySelector(".message-content").textContent = message;
    }
  }

  // Escape HTML to prevent XSS
  function escapeHtml(text) {
    var map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
  }

  // Handle "Enter" key for sending messages
  document.getElementById("user-input").addEventListener("keypress", function(e) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      document.getElementById("send-btn").click();
    }
  });

  // Back-to-Top Button functionality
  var backToTopBtn = document.getElementById("backToTopBtn");
  window.onscroll = function() { scrollFunction(); };
  function scrollFunction() {
    if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
      backToTopBtn.style.display = "block";
    } else {
      backToTopBtn.style.display = "none";
    }
  }

  // Generated Images Array – update these URLs as needed
  const generatedImages = [
    'static/images/me-pics/a2.png',
    'static/images/me-pics/a3.png',
    'static/images/me-pics/a4.png',
    'static/images/me-pics/a5.png',
    'static/images/me-pics/a6.png',
    'static/images/me-pics/a8.png',
    'static/images/me-pics/a9.png',
    'static/images/me-pics/a10.png',
    'static/images/me-pics/a11.png',

  ];

  let circleImages = []; // To store references to the circle images
  const circleContainer = document.getElementById('circleContainer');

  // Create the rotating circle of images dynamically with proper spacing.
  function createCircleImages() {
    circleContainer.innerHTML = '';
    circleImages = [];
    const numImages = 8; // Number of images around the circle

    // Track used images
    let usedImages = [];

    // Determine effective radius: container radius - inner alert radius - fixed gap (20px)
    const containerWidth = circleContainer.clientWidth;
    const containerRadius = containerWidth / 2;
    const innerAlertElem = document.querySelector('.inner-alert');
    const innerAlertRadius = innerAlertElem.clientWidth / 2;
    const gap = 20;
    const effectiveRadius = containerRadius - innerAlertRadius - gap;

    // Get the computed circle image size from the custom property
    const computedSize = getComputedStyle(document.documentElement).getPropertyValue('--circle-img-size').trim();
    let circleImgSize = parseFloat(computedSize);
    if (isNaN(circleImgSize)) {
      circleImgSize = 90;
    }
    const offset = circleImgSize / 2; // Half the size for centering

    for (let i = 0; i < numImages; i++) {
      const img = document.createElement('img');

      // Pick a random image that hasn't been used yet
      let randomImageIndex;
      do {
        randomImageIndex = Math.floor(Math.random() * generatedImages.length);
      } while (usedImages.includes(randomImageIndex));

      img.src = generatedImages[randomImageIndex];
      usedImages.push(randomImageIndex);  // Track the image as used

      img.classList.add('circle-image');
      const angle = (360 / numImages) * i;
      const radian = angle * (Math.PI / 180);
      const x = effectiveRadius * Math.cos(radian);
      const y = effectiveRadius * Math.sin(radian);
      img.style.left = `calc(50% + ${x}px - ${offset}px)`;
      img.style.top = `calc(50% + ${y}px - ${offset}px)`;
      circleContainer.appendChild(img);
      circleImages.push(img);
    }
  }

  // Animate two random images from the circle to exit toward target elements
  function animateExitToTargets() {
    let indices = [];
    while (indices.length < 2) {
      const rand = Math.floor(Math.random() * circleImages.length);
      if (!indices.includes(rand)) indices.push(rand);
    }
    const [firstIndex, secondIndex] = indices;
    const img1 = circleImages[firstIndex];
    const img2 = circleImages[secondIndex];

    const mainTarget = document.getElementById('mainProfilePic').getBoundingClientRect();
    const aboutTarget = document.getElementById('aboutProfilePic').getBoundingClientRect();

    const img1Rect = img1.getBoundingClientRect();
    const img2Rect = img2.getBoundingClientRect();

    const translateX1 = (mainTarget.left + mainTarget.width / 2) - (img1Rect.left + img1Rect.width / 2);
    const translateY1 = (mainTarget.top + mainTarget.height / 2) - (img1Rect.top + img1Rect.height / 2);
    const translateX2 = (aboutTarget.left + aboutTarget.width / 2) - (img2Rect.left + img2Rect.width / 2);
    const translateY2 = (aboutTarget.top + aboutTarget.height / 2) - (img2Rect.top + img2Rect.height / 2);

    img1.style.transition = 'transform 1.5s ease';
    img2.style.transition = 'transform 1.5s ease';
    img1.style.transform = `translate(${translateX1}px, ${translateY1}px) scale(1.2)`;
    img2.style.transform = `translate(${translateX2}px, ${translateY2}px) scale(1.2)`;

    setTimeout(() => {
      document.getElementById('mainProfilePic').src = img1.src;
      document.getElementById('aboutProfilePic').src = img2.src;
      const splash = document.getElementById('splash');
      splash.style.transition = 'opacity 1s ease';
      splash.style.opacity = 0;
      setTimeout(() => {
        splash.style.display = 'none';
      }, 1000);
    }, 1500);
  }

  // Initialize splash animation on page load
  window.addEventListener('load', () => {
    createCircleImages();
    setTimeout(animateExitToTargets, 5000);
  });
</script>
</body>
</html>
