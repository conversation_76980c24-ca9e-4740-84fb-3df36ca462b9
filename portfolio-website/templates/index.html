<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> - AI Portfolio</title>
  <meta name="description" content="<PERSON><PERSON><PERSON> Gupta AI Portfolio">
  <meta name="author" content="<PERSON>ks<PERSON>">

  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">

  <!-- Additional CSS Files -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/templatemo-digimedia-v1.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animated.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animate.min.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/owl.css') }}">

  <!-- Custom Inline Styles for Extra Responsiveness -->
  <style>
    /* Adjust the floating chat window on smaller screens */
    @media (max-width: 768px) {
      .floating-chat-window {
        width: 90%;
        right: 5%;
      }
    }
    
  </style>
</head>

<body>

  <!-- ***** Header / Navbar ***** -->
  <header>
    <nav class="navbar navbar-expand-lg fixed-top">
      <div class="container">
        <a class="navbar-brand" href="#top">
          <span class="logo-text">Akshat Gupta</span>
        </a>
    
        <!-- Mobile Menu Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
    
        <!-- Navbar Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="#top">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#about">About</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#academics">Acadamic Highlights</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#research">Research</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#skills">Skills</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#projects">Projects</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#contact">Contact</a>
            </li>
            <!-- Download Resume Button -->
            <li class="nav-item">
              <a class="nav-link download-resume" href="{{ url_for('static', filename='images/resume.pdf') }}" download>Download Resume</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>


<!-- ***** Main Banner Area ***** -->
<div id="top" class="main-banner">
  <div class="container">
    <div class="row align-items-center">
      <!-- Text Section (Left Side) -->
      <div class="col-lg-6">
        <h6 class="wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s" 
            style="text-transform: uppercase; color: #ffffff; font-size: 22px; font-weight: 700; letter-spacing: 2px;">
          Welcome to My Portfolio
        </h6>
        <h2>
          <span style="color: #ffffff;">I'm</span> 
          <span id="animated-name">
            <span>Akshat Gupta</span>
            <span>अक्षत गुप्ता</span>
          </span>
        </h2>
        <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
          AI Engineer | ML Enthusiast | Innovator
        </p>
        <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
          Explore my portfolio to see the latest projects where I applied AI to drive real-world impact and bring innovation to various domains.
        </p>
        <a href="#projects" class="btn btn-outline-light wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.4s">View my work</a>
      </div>
      
      <!-- Image Section (Right Side) -->
      <div class="col-lg-6">
        <img src="static/images/image.png" alt="Profile Image" class="banner-image">
      </div>

    </div>
  </div>
</div>
<!-- ***** End Main Banner ***** -->


 <!-- ***** About Section ***** -->
<!-- ***** About Section ***** -->
<section id="about" class="about section">
  <div class="container">
    <!-- Section Title -->
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        About Me
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        AI Engineer • Innovator • Lifelong Learner
      </p>
    </div>

    <!-- Interactive About Card -->
    <div class="about-card-wrapper wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.7s">
      <div class="about-card">
        <!-- Profile Image Column -->
        <div class="about-image">
          <img src="static/images/profile_pic.png" alt="Profile Image" class="img-fluid">
        </div>
        <!-- Text Content Column -->
        <div class="about-info">
          <h3>Welcome to my journey</h3>
          <p>
            I am a passionate AI Engineer dedicated to crafting innovative solutions that blend technology with creativity. My work focuses on developing advanced machine learning and deep learning models that solve real-world challenges.
          </p>
          <p>
            When I'm not immersed in coding and research, I love exploring the latest trends in AI, experimenting with new projects, and finding inspiration in everyday life. My journey in AI is driven by curiosity and a commitment to continuous learning.
          </p>
          <p>
            I'm currently pursuing my B.Tech in Computer Science &amp; Engineering at Bennett University, which has provided a strong technical foundation. However, my true passion lies in applying AI to create transformative, real-world applications.
          </p>
          <a href="#contact" class="btn about-cta">Let’s Connect</a>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- ***** End About Section ***** -->
  <!-- ***** Academic Highlights Section ***** -->
<!-- Academic Highlights Section -->
<section id="academics" class="academics section">
  <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Academic Highlights</h2>
      <p class="text-center mb-5 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          A showcase of my academic performance and certifications.
      </p>

      <!-- Background Decorative Waves -->
      <div class="academic-bg">
          <svg viewBox="0 0 1440 320"><path fill="rgba(255, 235, 200, 0.5)" d="M0,192L1440,64L1440,320L0,320Z"></path></svg>
      </div>

      <div class="row g-4">
          <!-- CGPA Card -->
          <div class="col-lg-4 col-md-6">
              <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
                  <div class="academic-icon"><i class="fas fa-graduation-cap"></i></div>
                  <h4>Current CGPA</h4>
                  <p>8.64 / 10</p>
              </div>
          </div>

          <!-- University Card -->
          <div class="col-lg-4 col-md-6">
              <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                  <div class="academic-icon"><i class="fas fa-university"></i></div>
                  <h4>Bennett University</h4>
                  <p>B.Tech, Computer Science & Engineering (2022 - 2026)</p>
              </div>
          </div>

          <!-- 12th Grade Card -->
          <div class="col-lg-4 col-md-6">
              <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
                  <div class="academic-icon"><i class="fas fa-school"></i></div>
                  <h4>Higher Secondary</h4>
                  <p>90% (CBSE, 2022)</p>
              </div>
          </div>

          <!-- 10th Grade Card -->
          <div class="col-lg-4 col-md-6">
              <div class="academic-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
                  <div class="academic-icon"><i class="fas fa-book"></i></div>
                  <h4>Senior Secondary</h4>
                  <p>92% (CBSE, 2020)</p>
              </div>
          </div>

          <!-- Certifications Integrated -->
          <div class="col-lg-8">
              <div class="certifications-section wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.1s">
                  <h4><i class="fas fa-certificate"></i> Certifications</h4>
                  <div class="certifications-list">
                      <div class="cert-item">
                          <i class="fas fa-award cert-icon"></i>
                          <span class="cert-name">Machine Learning by IBM</span>
                      </div>
                      <div class="cert-item">
                          <i class="fas fa-award cert-icon"></i>
                          <span class="cert-name">Deep Learning by Google</span>
                      </div>
                      <div class="cert-item">
                          <i class="fas fa-award cert-icon"></i>
                          <span class="cert-name">Networking by Coursera</span>
                      </div>
                      <div class="cert-item">
                          <i class="fas fa-award cert-icon"></i>
                          <span class="cert-name">Supervised ML by IBM</span>
                      </div>
                      <div class="cert-item">
                          <i class="fas fa-award cert-icon"></i>
                          <span class="cert-name">Convolutional Neural Networks (TensorFlow)</span>
                      </div>
                  </div>
              </div>
          </div>

      </div>
  </div>
</section>
<!-- ***** Research Papers Section ***** -->
<section id="research" class="research section">
  <div class="container">
    <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Research Papers (Under Work)</h2>
    <p class="text-center mb-5 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
        Below are the research papers that I am currently working on.
    </p>

    <div class="row g-4">
      <!-- Research Paper 1 -->
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <h4>Research Paper Title 1</h4>
          <p><strong>Research Area:</strong> AI, Machine Learning</p>
          <p><strong>Abstract:</strong> This paper discusses the advancements in machine learning algorithms, focusing on new architectures in deep learning.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
            <a href="https://github.com/your-research-repo" class="btn btn-outline-dark btn-sm" target="_blank">GitHub Repo</a>
          </div>
        </div>
      </div>

      <!-- Research Paper 2 -->
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          <h4>Research Paper Title 2</h4>
          <p><strong>Research Area:</strong> Deep Learning</p>
          <p><strong>Abstract:</strong> An exploration of new techniques in deep neural networks for improved image recognition and classification.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
            <a href="https://github.com/your-research-repo" class="btn btn-outline-dark btn-sm" target="_blank">GitHub Repo</a>
          </div>
        </div>
      </div>

      <!-- Research Paper 3 -->
      <div class="col-lg-4 col-md-6">
        <div class="research-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <h4>Research Paper Title 3</h4>
          <p><strong>Research Area:</strong> Natural Language Processing</p>
          <p><strong>Abstract:</strong> This paper investigates novel approaches to enhance text understanding using transformers and attention mechanisms.</p>
          <div class="research-links">
            <a href="#" class="btn btn-outline-dark btn-sm" target="_blank">Work in Progress</a>
            <a href="https://github.com/your-research-repo" class="btn btn-outline-dark btn-sm" target="_blank">GitHub Repo</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- ***** End Research Papers Section ***** -->
  <!-- ***** Skills Section ***** -->
<!-- Skills Section -->
<section id="skills" class="skills section">
  <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Technical Skills</h2>
      <p class="text-center mb-5 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          My expertise in AI, deep learning, generative models, and software development.
      </p>

      <div class="row g-4 justify-content-center">
          <!-- Machine Learning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/3066/3066305.png" alt="Machine Learning">
                  </div>
                  <h4>Machine Learning</h4>
                  <p>Supervised & Unsupervised Learning, Classification, Regression</p>
              </div>
          </div>

          <!-- Deep Learning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/3242/3242257.png" alt="Deep Learning">
                  </div>
                  <h4>Deep Learning</h4>
                  <p>Neural Networks, CNNs, RNNs, LSTMs, GANs</p>
              </div>
          </div>

          <!-- Generative AI & Fine-Tuning -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/7017/7017303.png" alt="Generative AI">
                  </div>
                  <h4>Generative AI & Fine-Tuning</h4>
                  <p>
                      Hugging Face, LoRA Models, OpenAI API, Fine-tuning GPT, Stable Diffusion
                  </p>
              </div>
          </div>

          <!-- NLP & LLMs -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/1730/1730153.png" alt="NLP & LLMs">
                  </div>
                  <h4>NLP & Large Language Models</h4>
                  <p>Transformers, BERT, GPT, RAG, LangChain</p>
              </div>
          </div>

          <!-- Computer Vision -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.1s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/1680/1680292.png" alt="Computer Vision">
                  </div>
                  <h4>Computer Vision</h4>
                  <p>OpenCV, YOLO, Image Segmentation, Object Detection</p>
              </div>
          </div>

          <!-- Cloud & APIs -->
          <div class="col-lg-4 col-md-6">
              <div class="skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.3s">
                  <div class="skill-icon">
                      <img src="https://cdn-icons-png.flaticon.com/512/2329/2329035.png" alt="Cloud & APIs">
                  </div>
                  <h4>Cloud & APIs</h4>
                  <p>Google Cloud, AWS, OpenAI API, Hugging Face API</p>
              </div>
          </div>
      </div>
  </div>
</section>
  <!-- ***** End Skills Section ***** -->

  <!-- ***** Projects Section ***** -->
  <div id="projects" class="projects section">
    <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">My Projects</h2>
      <p class="text-center mb-5">Here are some of my featured projects that showcase my skills and expertise.</p>
      <div class="row">
        <!-- Project 1 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/Akshar.png') }}" alt="Project 1" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>Akshar</h4>
                <p>A chat app that translates text and audio messages in real time, built using Flutter.</p>
                <div class="tech-stack">
                  <span class="badge">Flutter</span>
                  <span class="badge">Dart</span>
                  <span class="badge">AI</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
        <!-- Project 2 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/eye_detection.png') }}" alt="Project 2" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>Eye Disease Detection</h4>
                <p>Empowering precise eye disease detection through cutting-edge generative AI and deep learning on fundus images.</p>
                <div class="tech-stack">
                  <span class="badge">Deep Learning</span>
                  <span class="badge">Generative AI</span>
                  <span class="badge">Python</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/medical_chatbot.png') }}" alt="Project 1" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>Medical ChatBot</h4>
                <p>Medcal ChatBot is an context-aware medical assistant that leverages retrieval-augmented generation with a locally stored vector database (built from PDFs) to deliver dynamic, AI-powered medical advice through a modern, conversational interface</p>
                <div class="tech-stack">
                  <span class="badge">RAG</span>
                  <span class="badge">Python</span>
                  <span class="badge">LangChain</span>
                  <span class="badge">Generative AI</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/Medical-ChatBOT" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
        <!-- Project 3 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/FacePay.png') }}" alt="Project 3" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>FacePay</h4>
                <p>An AI-based payment system using facial recognition for secure UPI transactions.</p>
                <div class="tech-stack">
                  <span class="badge">DeepFace</span>
                  <span class="badge">Flask</span>
                  <span class="badge">SQLite3</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Projects Section ***** -->
  <!-- ***** Contact Section ***** -->
 <!-- Contact Section -->
 <section id="contact" class="contact section">
  <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Get in Touch</h2>
      <p class="text-center wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          Have a project, question, or collaboration idea? Feel free to reach out.
      </p>

      <div class="contact-container">
          <div class="row align-items-center">
              <!-- Contact Information -->
              <div class="col-lg-5">
                  <div class="contact-info wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s">
                      <h4>Contact Details</h4>

                      <div class="contact-item">
                          <i class="fas fa-envelope"></i>
                          <a href="mailto:<EMAIL>" class="contact-email">Email</a>
                      </div>

                      <div class="contact-item">
                          <i class="fab fa-linkedin"></i>
                          <a href="https://www.linkedin.com/in/akshat-gupta-077a7b256/" target="_blank">
                              LinkedIn
                          </a>
                  
                      </div>

                      <div class="contact-item">
                          <i class="fab fa-github"></i>
                          <a href="https://github.com/Akshat-Gupta04" target="_blank"> Github</a>
                      </div>

                      <div class="contact-item">
                          <i class="fas fa-map-marker-alt"></i>
                          <p>Location</p>
                          <span>Greater Noida, India</span>
                      </div>
                  </div>
              </div>

              <!-- Contact Form -->
              <div class="col-lg-7">
                  <div class="contact-form wow fadeInRight" data-wow-duration="1s" data-wow-delay="0.5s">
                      <h4>Send a Message</h4>

                      <form action="mailto:<EMAIL>" method="post" enctype="text/plain">
                          <div class="row">
                              <div class="col-md-6 mb-3">
                                  <input type="text" name="name" class="form-control" placeholder="Your Name" required>
                              </div>
                              <div class="col-md-6 mb-3">
                                  <input type="email" name="email" class="form-control" placeholder="Your Email" required>
                              </div>
                          </div>
                          <div class="mb-3">
                              <textarea name="message" class="form-control" rows="4" placeholder="Your Message" required></textarea>
                          </div>
                          <div class="text-center">
                              <button type="submit" class="btn contact-btn">Send Message</button>
                          </div>
                      </form>
                  </div>
              </div>
          </div>
      </div>
  </div>
</section>
  <!-- ***** End Contact Section ***** -->



  <!-- Floating Chat Button -->
  <div class="floating-chat-btn">
    <button id="chat-toggle" class="btn">
      <img src="{{ url_for('static', filename='images/chat.svg') }}" alt="Chat Bot Icon">
    </button>
  </div>

  <!-- Floating Chat Window -->
  <div id="chat-window" class="floating-chat-window">
    <div class="chat-header">
      <h5>AI Assistant</h5>
      <button id="close-chat" class="close-btn">&times;</button>
    </div>
    <div class="chat-body" id="chat-body"></div>
    <div class="chat-input">
      <textarea id="user-input" placeholder="Type your message..." rows="1"></textarea>
      <button id="send-btn">Send</button>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="container text-center">
      <p class="wow fadeIn" data-wow-duration="1s" data-wow-delay="0.3s">
        2025 © Akshat Gupta. All Rights Reserved.
      </p>
      <div class="social-links mt-3">
        <a href="mailto:<EMAIL>" target="_blank" class="mx-2">
          <img src="{{ url_for('static', filename='images/mail.svg') }}" alt="Email Icon" style="width: 24px; height: 24px;">
        </a>
        <a href="https://github.com/Akshat-Gupta04" target="_blank" class="mx-2">
          <img src="{{ url_for('static', filename='images/github.svg') }}" alt="GitHub Icon" style="width: 24px; height: 24px;">
        </a>
        <a href="tel:+9306199632" class="mx-2">
          <img src="{{ url_for('static', filename='images/alternate-phone.svg') }}" alt="Phone Icon" style="width: 24px; height: 24px;">
        </a>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
  <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/owl-carousel.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animation.js') }}"></script>
  <script src="{{ url_for('static', filename='js/imagesloaded.js') }}"></script>
  <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animated-name.js') }}"></script>

  <script>
    // Toggle chat window visibility
    document.getElementById("chat-toggle").addEventListener("click", function() {
      document.getElementById("chat-window").classList.toggle("show");
    });
    // Close chat window
    document.getElementById("close-chat").addEventListener("click", function() {
      document.getElementById("chat-window").classList.remove("show");
    });
    // Handle sending message
    document.getElementById("send-btn").addEventListener("click", async function() {
      var userInput = document.getElementById("user-input").value;
      if (userInput.trim() !== "") {
        addMessage(userInput, "user");
        document.getElementById("user-input").value = "";
        addMessage("Thinking...", "ai");
        try {
          const response = await fetch('/ask', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ question: userInput })
          });
          const data = await response.json();
          if (data.response) {
            updateAIResponse(data.response);
          } else {
            updateAIResponse("Sorry, I couldn't understand that.");
          }
        } catch (error) {
          updateAIResponse("Sorry, I'm having trouble connecting to the server.");
          console.error("Error:", error);
        }
      }
    });

    // Add message to the chat
    function addMessage(message, sender) {
      var chatBody = document.getElementById("chat-body");
      var messageElement = document.createElement("div");
      messageElement.classList.add("message", sender);
      if (sender === "user") {
        messageElement.innerHTML = `
          <div class="message-content">${escapeHtml(message)}</div>
          <img src="{{ url_for('static', filename='images/person.svg') }}" alt="User Avatar" class="avatar">
        `;
      } else if (sender === "ai") {
        messageElement.innerHTML = `
          <img src="{{ url_for('static', filename='images/ai.svg') }}" alt="AI Avatar" class="avatar">
          <div class="message-content">${escapeHtml(message)}</div>
        `;
      }
      chatBody.appendChild(messageElement);
      chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Update the last AI message with the response
    function updateAIResponse(message) {
      var chatBody = document.getElementById("chat-body");
      var aiMessageElement = chatBody.querySelector(".message.ai:last-child");
      if (aiMessageElement) {
        aiMessageElement.querySelector(".message-content").textContent = message;
      }
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
      var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    // Handle "Enter" key for sending messages
    document.getElementById("user-input").addEventListener("keypress", function(e) {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        document.getElementById("send-btn").click();
      }
    });

    // Back-to-Top Button functionality
    var backToTopBtn = document.getElementById("backToTopBtn");
    window.onscroll = function() { scrollFunction(); };
    function scrollFunction() {
      if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
        backToTopBtn.style.display = "block";
      } else {
        backToTopBtn.style.display = "none";
      }
    }
    function topFunction() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
   
  </script>
  
</body>
</html>