/* General Styles */
body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.7;
  background-color: #f8f9fc;
  padding-top: 70px; /* Adjust based on navbar height */
}

.section {
  padding: 100px 0;
}

/* Navbar Styles */
.navbar {
  z-index: 1050; /* Ensure navbar stays above other elements */
}
/* Roll-On Animation for Name */
#animated-name {
  font-size: 2.5rem; /* Adjusted size */
  font-weight: bold;
  color: #191970; /* Dark blue font color */
  overflow: hidden;
  white-space: nowrap;
  height: 3rem; /* Matches font size */
  position: relative;
  margin-left: 20px; /* Added space between "I'm" and the name */
}

#animated-name span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; /* Ensure the text spans across the container */
  text-align: left; /* Align text properly */
  animation: rollOn 4s linear infinite; /* Duration for two names */
  opacity: 0; /* Start hidden */
}

/* Keyframes for Roll-On Animation */
@keyframes rollOn {
  0% {
      opacity: 0;
      transform: translateY(100%);
  }
  10% {
      opacity: 1;
      transform: translateY(0);
  }
  40% {
      opacity: 1;
      transform: translateY(0);
  }
  50% {
      opacity: 0;
      transform: translateY(-100%);
  }
  100% {
      opacity: 0;
      transform: translateY(-100%);
  }
}

#animated-name span:nth-child(1) {
  animation-delay: 0s;
}
#animated-name span:nth-child(2) {
  animation-delay: 2s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  #animated-name {
      font-size: 2rem;
      height: 2.5rem;
  }
}

@media (max-width: 576px) {
  #animated-name {
      font-size: 1.8rem;
      height: 2rem;
  }
}

/* Hover Effects for Animated Name */
#animated-name:hover span {
  animation-play-state: paused;
}

#animated-name:hover {
  cursor: pointer;
}
/* Main Banner Styles */
.main-banner {
  background: linear-gradient(to bottom right, #fa68a5, #c9c9f5);
  color: #ffffff;
  padding: 120px 0;
  position: relative;
  text-align: left;
  
}


.main-banner h2,
.main-banner p {
  z-index: 2;
  position: relative;
}

.dark-text {
  color: #000000 !important;
}

.main-banner .btn {
  background: #ffffff;
  color: #ff3c83;
  font-weight: bold;
  border-radius: 50px;
  padding: 15px 35px;
  margin-top: 20px;
  transition: all 0.3s ease-in-out;
}

.main-banner .btn:hover {
  background: #ff3c83;
  color: #ffffff;
  transform: scale(1.05);
}

/* About Section Styles */
/* ---------------------------
   UNIQUE ABOUT SECTION STYLES
---------------------------- */
/* ---------------------------
   UNIQUE ABOUT SECTION STYLES
---------------------------- */
.unique-about-section {
  position: relative;
  /* Softer pastel gradient (blue to pink) */
  background: linear-gradient(135deg, #e0f7fa, #ffeef2);
  padding: 100px 0;
  overflow: hidden;
}

/* Optional Decorative Waves */
.about-top-wave,
.about-bottom-wave {
  position: absolute;
  width: 100%;
  height: 120px;
  left: 0;
  z-index: 1;
  background-size: cover;
  background-repeat: no-repeat;
}

.about-top-wave {
  top: 0;
  /* Example wave; replace with any other if desired */
  background-image: url("https://www.svgrepo.com/download/448147/wave-form-2.svg");
  transform: rotate(180deg);
}

.about-bottom-wave {
  bottom: 0;
  background-image: url("https://www.svgrepo.com/download/448147/wave-form-2.svg");
}

/* Section Title & Subtitle */
.section-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #2c3e50;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #555555;
}

/* Diagonal Card Container */
.unique-about-card {
  background: #ffffff;
  border-radius: 25px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2; /* Ensure above wave shapes */
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  padding: 40px;
  margin: 0 auto;
  max-width: 1000px;
  transform: skew(-2deg); /* Subtle diagonal effect */
  transition: transform 0.3s ease;
}

.unique-about-card:hover {
  transform: skew(-1deg) translateY(-5px);
}

/* Fix child elements so text/images remain unskewed */
.unique-about-card > * {
  transform: skew(2deg);
}

/* Profile Image Column */
.about-image-col {
  flex: 1 1 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.image-wrapper {
  width: 100%;
  max-width: 350px; /* Adjust as needed */
  overflow: hidden;
  border-radius: 10px; /* Soft corners */
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
  transition: transform 0.4s ease;
}

.image-wrapper img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

.image-wrapper:hover {
  transform: scale(1.03);
}

/* Text Content Column */
.about-info-col {
  flex: 2 1 400px;
  padding: 0 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Title inside the card */
.about-info-col h3 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #d35400;
}

/* Main paragraph */
.about-lead {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #444;
  margin-bottom: 30px;
}

/* Stats Container & Items */
.about-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  align-items: flex-start;
}

.about-stat-item {
  flex: 1 1 120px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.stat-icon {
  font-size: 2rem;
  color: #ff5733;
  min-width: 40px;
}

.about-stat-item h4 {
  margin: 0 0 3px;
  font-size: 1.1rem;
  color: #333;
  font-weight: 700;
}

.about-stat-item p {
  margin: 0;
  font-size: 0.95rem;
  color: #666;
}

/* CTA Button */
.about-cta {
  background: #ff5733;
  color: #ffffff;
  font-weight: 600;
  border: none;
  padding: 12px 30px;
  border-radius: 30px;
  transition: background 0.3s ease, transform 0.3s ease;
  display: inline-block;
  text-decoration: none;
}

.about-cta:hover {
  background: #e74c3c;
  transform: translateY(-3px);
}

/* Media Queries for Responsiveness */
@media (max-width: 992px) {
  .unique-about-card {
    flex-direction: column;
    padding: 30px;
  }
  .about-info-col {
    padding: 20px 0 0 0;
  }
  .about-stats {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 2rem;
  }
  .about-info-col h3 {
    font-size: 1.7rem;
  }
  .about-lead {
    font-size: 0.95rem;
  }
  .about-stat-item h4 {
    font-size: 1rem;
  }
  .about-stat-item p {
    font-size: 0.85rem;
  }
}
/* Skills Section Styles */
/* Skills Section Styling */
.skills {
  position: relative;
  background: linear-gradient(135deg, #1B1B2F, #162447); /* AI-Inspired Dark Gradient */
  padding: 100px 0;
  text-align: center;
  color: white;
}

/* Floating AI Glow */
.skills::before {
  content: "";
  position: absolute;
  top: -50px;
  left: -50px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 0, 150, 0.3) 20%, transparent 70%);
  z-index: 0;
  border-radius: 50%;
  animation: floatingGlow 5s infinite alternate ease-in-out;
}

@keyframes floatingGlow {
  from { transform: translateY(0px); }
  to { transform: translateY(20px); }
}

/* Skill Cards */
.skill-card {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  z-index: 1;
  margin-bottom: 40px; /* Added space between rows */
}

.skill-card:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 30px rgba(255, 0, 150, 0.3);
}

/* Skill Icons */
.skill-icon {
  position: relative;
  display: inline-block;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.skill-card:hover .skill-icon {
  background: rgba(255, 0, 150, 0.3);
  transform: scale(1.1);
}

.skill-icon img {
  width: 100%;
  height: auto;
}

/* Skill Titles */
.skill-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #FF4081;
}

/* Skill Descriptions */
.skill-card p {
  font-size: 1rem;
  color: #ddd;
}

/* AI Effect */
.skill-card::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 0, 150, 0.3), transparent);
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0;
}

.skill-card:hover::before {
  opacity: 1;
  transform: scale(1.5);
}

/* Spacing Between Rows */
.row {
  margin-bottom: 50px; /* Space between rows */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .skill-card {
      padding: 20px;
  }

  .skill-icon {
      width: 60px;
      height: 60px;
  }

  .skill-card h4 {
      font-size: 1.2rem;
  }
}
/* Projects Section Styles */
/* Projects Section */
.projects {
  background: linear-gradient(to bottom left, #fba784, #a4e1d3);
  padding: 100px 0;
}

.project-item {
  position: relative;
}

.project-card {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.2);
}

.project-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-overlay h4 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.project-overlay p {
  font-size: 0.9rem;
  margin-bottom: 15px;
  text-align: center;
}

.tech-stack .badge {
  display: inline-block;
  margin: 5px;
  padding: 5px 10px;
  background: #ff3c83;
  color: white;
  font-size: 0.8rem;
  border-radius: 20px;
}

.tech-stack .badge:hover {
  background: #ff6a9c;
}

.btn-outline-light {
  border: 2px solid #ffffff;
  color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.btn-outline-light:hover {
  background-color: #ffffff;
  color: #ff3c83;
}

/* Certifications Section Styles */
.certifications {
  background: linear-gradient(to right, #ffe6f3, #f8d9e8);
  padding: 100px 0;
  color: #333;
}

/* Contact Section Styles */
/* Contact Section Styling */
.contact {
  position: relative;
  background: linear-gradient(135deg, #FFD3A5, #FFB6C1); /* Soft gradient background */
  padding: 100px 0;
  text-align: center;
  overflow: hidden;
}

/* Contact Container */
.contact-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 40px;
}

/* Contact Info Box */
.contact-info {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.contact-info h4 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

/* Contact Info Items */
.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.contact-item i {
  font-size: 1.8rem;
  color: #FF9800;
}

.contact-item p {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #555;
}

.contact-item a,
.contact-item span {
  font-size: 0.95rem;
  color: #333;
  text-decoration: none;
  font-weight: bold;
}/* Fix Email Overflow Issue */
.contact-email {
  display: inline-block;
  max-width: 100%;
  word-break: break-word; /* Ensures long words break properly */
  overflow-wrap: break-word; /* Ensures wrapping inside container */
  white-space: normal; /* Prevents single-line overflow */
  text-align: center;
  font-size: 1rem;
}

/* Ensure container properly contains text */
.contact-info {
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.contact-item a:hover {
  color: #FF9800;
}

/* Contact Form */
.contact-form {
  background: rgba(255, 255, 255, 0.3);
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.contact-form h4 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

/* Input Fields */
.contact-form .form-control {
  padding: 12px;
  border-radius: 10px;
  border: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.5);
}

.contact-form .form-control:focus {
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
  outline: none;
}

/* Send Message Button */
.contact-btn {
  background: #FF9800;
  color: white;
  padding: 12px 30px;
  font-size: 1rem;
  border-radius: 50px;
  font-weight: bold;
  transition: all 0.3s ease;
  border: none;
}

.contact-btn:hover {
  background: #E67E22;
  transform: scale(1.05);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .contact-info, .contact-form {
      margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .contact-info {
      padding: 20px;
  }

  .contact-form {
      padding: 20px;
  }
}
/* Updated Footer Styles: Light & Modern */
footer {
  background: #f5f5f5;          /* Light grey background */
  color: #555555;               /* Darker grey text for readability */
  padding: 30px 0;
  text-align: center;
  border-top: 1px solid #e0e0e0; /* Subtle top border */
}

footer p {
  margin: 0;
  font-size: 0.95rem;
}

/* Social Links in Footer */
footer .social-links {
  margin-top: 15px;
}

footer .social-links a {
  margin: 0 10px;
  color: inherit;             /* Inherit the footer text color */
  transition: color 0.3s ease, opacity 0.3s ease;
}

footer .social-links a:hover {
  color: #888888;             /* Slightly lighter grey on hover */
  opacity: 0.8;
}


/* Floating Chat Button Styles */
.floating-chat-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1060;
}

/* Updated Floating Chat Button Colors */
.floating-chat-btn .btn {
  background-color: #e5ddf3; /* Vibrant purple */
  border-radius: 50%;
  padding: 15px;
  border: none;
  color: white;
  font-size: 18px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.floating-chat-btn .btn:hover {
  background-color: #5a32a3; /* Slightly darker purple on hover */
  transform: scale(1.05);
}

/* Floating Chat Button Styles */
.floating-chat-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1060;
}

.floating-chat-btn .btn {
  background-color: #ff3c83; /* Theme Pink */
  border-radius: 50%;
  padding: 15px;
  border: none;
  color: white;
  font-size: 18px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s;
}

.floating-chat-btn .btn:hover {
  background-color: #e22f71;
}

/* Floating Chat Window Styles */
.floating-chat-window {
  display: none; /* Hidden by default */
  position: fixed;
  bottom: 90px;
  right: 30px;
  width: 350px;
  max-height: 500px;
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  z-index: 1060;
  overflow: hidden;
  border: 2px solid #ff3c83; /* Theme Border */
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

.floating-chat-window.show {
  opacity: 1;
  visibility: visible;
}

/* Chat Header Styles */
.chat-header {
  background-color: #ff3c83; /* Theme Pink */
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.chat-header h5 {
  margin: 0;
  font-size: 1.2rem;
}

.chat-header .close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Chat Body Styles */
.chat-body {
  padding: 15px 20px;
  flex: 1;
  overflow-y: auto;
  background: #fef7ff; /* Soft pink for the chat background */
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Message Styles */
.message {
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message .avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin: 0 10px;
}

.message .message-content {
  max-width: 70%;
  background-color: #ff3c83; /* Theme Pink for AI */
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  position: relative;
}

.message.user .message-content {
  background-color: #00c4cc; /* Theme Blue-Green for User */
}

.message .message-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 0;
  height: 0;
}

.message.ai .message-content::after {
  left: -10px;
  border: 10px solid transparent;
  border-right-color: #ff3c83;
}

.message.user .message-content::after {
  right: -10px;
  border: 10px solid transparent;
  border-left-color: #00c4cc;
}

/* Timestamp Styles */
.timestamp {
  font-size: 0.75rem;
  color: #888;
  margin: 0 10px;
}

/* Chat Input Styles */
.chat-input {
  padding: 15px 20px;
  background-color: #ffeef2; /* Theme Soft Pink */
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.chat-input textarea {
  flex: 1;
  padding: 10px 15px;
  border-radius: 20px;
  border: 1px solid #ddd;
  resize: none;
  font-size: 0.95rem;
}

.chat-input button {
  padding: 10px 20px;
  background-color: #ff3c83; /* Theme Pink */
  border: none;
  color: white;
  font-size: 0.95rem;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chat-input button:hover {
  background-color: #e22f71;
}

/* Animations */
@keyframes fadeIn {
  from {
      opacity: 0;
      transform: translateY(10px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .floating-chat-window {
      width: 90%;
      right: 5%;
  }

  .chat-input textarea {
      width: 60%;
  }
}
/* Header Styles */
header {
  position: relative;
  z-index: 1050;
}

.navbar {
  background: linear-gradient(90deg, #1d3557, #457b9d);
  transition: background 0.3s ease, box-shadow 0.3s ease;
}

.navbar-brand .logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  background: linear-gradient(to right, #a8dadc, #f4a261);
  background-clip: text; /* Standard property */
  -webkit-background-clip: text; /* For WebKit-based browsers */
  -webkit-text-fill-color: transparent;
  animation: logoAnimation 5s linear infinite;
}

@keyframes logoAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.navbar .nav-link {
  font-size: 1rem;
  font-weight: 500;
  color: #f1faee;
  transition: color 0.3s ease, transform 0.3s ease;
}

.navbar .nav-link:hover {
  color: #f4a261;
  transform: translateY(-3px);
}

.navbar .nav-link.active {
  font-weight: 700;
  color: #e63946;
}

.navbar-toggler {
  border: none;
  background: #e63946;
}

.navbar-toggler-icon {
  background-image: linear-gradient(to right, #1d3557, #457b9d);
}

.navbar.sticky {
  background: #1d3557;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Sticky Navbar on Scroll */
.sticky {
  position: fixed;
  top: 0;
  width: 100%;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .navbar .nav-link {
    font-size: 0.9rem;
  }

  .navbar-brand .logo-text {
    font-size: 1.5rem;
  }
}
/* Distinct styling for the Download Resume link */
.navbar-nav .download-resume {
background-color: #ef3a95;  /* Vibrant warm orange */
color: #ffffff !important;  /* White text */
padding: 8px 15px;
border-radius: 30px;
font-weight: bold;
margin-left: 15px;
transition: background-color 0.3s ease;
border: none;
}
/* Custom Dropdown Button (Navbar Toggler) */
.navbar-toggler {
  border: none;
  background: none;
  padding: 8px;
  outline: none !important;
  box-shadow: none !important;
}

/* Custom Icon for Navbar Toggler */
.navbar-toggler-icon {
  width: 30px;
  height: 30px;
  background-image: none; /* Remove default Bootstrap icon */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

/* Custom Animated Burger Icon */
.navbar-toggler-icon::before,
.navbar-toggler-icon::after,
.navbar-toggler-icon span {
  content: "";
  display: block;
  background: #ffffff; /* White lines */
  height: 3px;
  width: 30px;
  border-radius: 3px;
  transition: all 0.3s ease-in-out;
}

/* Space between lines */
.navbar-toggler-icon span {
  margin: 5px 0;
}

/* Navbar Open Animation */
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon span {
  opacity: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::before {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::after {
  transform: rotate(-45deg) translate(5px, -5px);
}

.navbar-nav .download-resume:hover {
background-color: #b42c1d;  /* Slightly darker on hover */
color: #ffffff !important;
}
/* Academic Highlights Section Styles */
/* Academic Section Styling */
/* Academic Highlights & Certifications Section */
.academics {
  background: linear-gradient(135deg, #fdf3e1, #ffe0b2);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

/* Background Decorative Wave */
.academic-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  opacity: 0.5;
}

/* Academic Card Styling */
.academic-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.academic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.academic-icon {
  font-size: 2rem;
  color: #ff8c00;
  margin-bottom: 10px;
}

.academic-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  color: #444;
}

.academic-card p {
  font-size: 1.2rem;
  color: #ff7b00;
  font-weight: bold;
}

/* Certifications Integrated Section */
.certifications-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.certifications-section h4 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #444;
  margin-bottom: 15px;
}

.certifications-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: space-between;
}

.cert-item {
  display: flex;
  align-items: center;
  background: #fff6e5;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 48%;
  justify-content: space-between;
}

.cert-icon {
  font-size: 1.5rem;
  color: #ff8c00;
}

.cert-name {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
  flex-grow: 1;
  margin-left: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .academic-card,
  .certifications-section {
      padding: 15px;
  }

  .academic-card h4,
  .certifications-section h4 {
      font-size: 1.3rem;
  }

  .cert-name {
      font-size: 0.9rem;
  }

  .certifications-list {
      flex-direction: column;
  }

  .cert-item {
      width: 100%;
  }
}
/* Research Papers Section Styles */
/* Research Papers Section Styles */
.research {
  background: #c8dcf9; /* Light neutral background */
  padding: 100px 0;
  text-align: center;
  color: #333; /* Dark text for readability */
}

/* Research Paper Card */
.research-card {
  background: #ffffff; /* White background for cards */
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: left;
}

.research-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.research-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  color: #2d3e50; /* Dark blue-gray for professionalism */
}

.research-card p {
  font-size: 1rem;
  color: #555; /* Slightly lighter text for descriptions */
  margin-bottom: 20px;
}

.research-links .btn {
  margin-top: 10px;
  background-color: #6c757d; /* Professional dark gray for buttons */
  color: #ffffff;
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 0.9rem;
  border: 2px solid #6c757d; /* Gray border for buttons */
}

.research-links .btn:hover {
  background-color: #5a6268; /* Darker gray on hover */
  border-color: #5a6268;
}

/* Responsive Adjustments for Research Section */
@media (max-width: 768px) {
  .research-card {
      padding: 20px;
  }

  .research-card h4 {
      font-size: 1.2rem;
  }
}