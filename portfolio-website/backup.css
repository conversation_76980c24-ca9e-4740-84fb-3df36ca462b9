/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.7;
    background-color: #f8f9fc;
    padding-top: 70px; /* Adjust based on navbar height */
}

.section {
    padding: 100px 0;
}

/* Navbar Styles */
.navbar {
    z-index: 1050; /* Ensure navbar stays above other elements */
}
/* Roll-On Animation for Name */
#animated-name {
    font-size: 2.5rem; /* Adjusted size */
    font-weight: bold;
    color: #191970; /* Dark blue font color */
    overflow: hidden;
    white-space: nowrap;
    height: 3rem; /* Matches font size */
    position: relative;
    margin-left: 20px; /* Added space between "I'm" and the name */
}

#animated-name span {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%; /* Ensure the text spans across the container */
    text-align: left; /* Align text properly */
    animation: rollOn 4s linear infinite; /* Duration for two names */
    opacity: 0; /* Start hidden */
}

/* Keyframes for Roll-On Animation */
@keyframes rollOn {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }
    10% {
        opacity: 1;
        transform: translateY(0);
    }
    40% {
        opacity: 1;
        transform: translateY(0);
    }
    50% {
        opacity: 0;
        transform: translateY(-100%);
    }
    100% {
        opacity: 0;
        transform: translateY(-100%);
    }
}

#animated-name span:nth-child(1) {
    animation-delay: 0s;
}
#animated-name span:nth-child(2) {
    animation-delay: 2s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #animated-name {
        font-size: 2rem;
        height: 2.5rem;
    }
}

@media (max-width: 576px) {
    #animated-name {
        font-size: 1.8rem;
        height: 2rem;
    }
}

/* Hover Effects for Animated Name */
#animated-name:hover span {
    animation-play-state: paused;
}

#animated-name:hover {
    cursor: pointer;
}
/* Main Banner Styles */
.main-banner {
    background: linear-gradient(to bottom right, #ff7eb3, #ff758c, #ff3c83);
    color: #ffffff;
    padding: 120px 0;
    position: relative;
    text-align: left;
}

.main-banner h2,
.main-banner p {
    z-index: 2;
    position: relative;
}

.dark-text {
    color: #000000 !important;
}

.main-banner .btn {
    background: #ffffff;
    color: #ff3c83;
    font-weight: bold;
    border-radius: 50px;
    padding: 15px 35px;
    margin-top: 20px;
    transition: all 0.3s ease-in-out;
}

.main-banner .btn:hover {
    background: #ff3c83;
    color: #ffffff;
    transform: scale(1.05);
}

/* About Section Styles */
.about-section {
    background: linear-gradient(to right, #fef7ff, #ffeef2);
    color: #444;
}

/* Skills Section Styles */
.skills {
    background: #ffffff;
    padding: 120px 0;
}

.skills .skill-item {
    margin-bottom: 50px;
}

.skills .skill-item img {
    transition: transform 0.3s ease;
}

.skills .skill-item:hover img {
    transform: scale(1.15);
}

/* Projects Section Styles */
/* Projects Section */
.projects {
    background: linear-gradient(to bottom left, #ffdccd, #fff1e1);
    padding: 100px 0;
  }
  
  .project-item {
    position: relative;
  }
  
  .project-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.2);
  }
  
  .project-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
  }
  
  .project-card:hover .project-image {
    transform: scale(1.1);
  }
  
  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .project-card:hover .project-overlay {
    opacity: 1;
  }
  
  .project-overlay h4 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .project-overlay p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .tech-stack .badge {
    display: inline-block;
    margin: 5px;
    padding: 5px 10px;
    background: #ff3c83;
    color: white;
    font-size: 0.8rem;
    border-radius: 20px;
  }
  
  .tech-stack .badge:hover {
    background: #ff6a9c;
  }
  
  .btn-outline-light {
    border: 2px solid #ffffff;
    color: #ffffff;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  .btn-outline-light:hover {
    background-color: #ffffff;
    color: #ff3c83;
  }

/* Certifications Section Styles */
.certifications {
    background: linear-gradient(to right, #ffe6f3, #f8d9e8);
    padding: 100px 0;
    color: #333;
}

/* Contact Section Styles */
.contact {
    background: #ffffff;
}

.contact .form-control {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.contact .btn {
    background: #ff3c83;
    color: #ffffff;
    border-radius: 50px;
    font-weight: bold;
}

.contact .btn:hover {
    background: #ffffff;
    color: #ff3c83;
    border: 1px solid #ff3c83;
}

/* Footer Styles */
footer {
    background: #222;
    color: #aaa;
    padding: 30px 0;
}

footer p {
    margin: 0;
}

/* Back-to-Top Button Styles */
#backToTopBtn {
    display: none; /* Hidden by default */
    position: fixed;
    bottom: 80px;
    left: 30px;
    z-index: 99;
    font-size: 18px;
    border: none;
    outline: none;
    background-color: #ff3c83;
    color: white;
    cursor: pointer;
    padding: 15px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

#backToTopBtn:hover {
    background-color: #e22f71;
}

/* Floating Chat Button Styles */
.floating-chat-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1060;
}

.floating-chat-btn .btn {
    background-color: #007bff; /* Primary Blue */
    border-radius: 50%;
    padding: 15px;
    border: none;
    color: white;
    font-size: 18px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s;
}

.floating-chat-btn .btn:hover {
    background-color: #0056b3;
    color: #ffffff;
}

/* Floating Chat Button Styles */
.floating-chat-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1060;
}

.floating-chat-btn .btn {
    background-color: #ff3c83; /* Theme Pink */
    border-radius: 50%;
    padding: 15px;
    border: none;
    color: white;
    font-size: 18px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s;
}

.floating-chat-btn .btn:hover {
    background-color: #e22f71;
}

/* Floating Chat Window Styles */
.floating-chat-window {
    display: none; /* Hidden by default */
    position: fixed;
    bottom: 90px;
    right: 30px;
    width: 350px;
    max-height: 500px;
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1060;
    overflow: hidden;
    border: 2px solid #ff3c83; /* Theme Border */
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.floating-chat-window.show {
    opacity: 1;
    visibility: visible;
}

/* Chat Header Styles */
.chat-header {
    background-color: #ff3c83; /* Theme Pink */
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.chat-header h5 {
    margin: 0;
    font-size: 1.2rem;
}

.chat-header .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Chat Body Styles */
.chat-body {
    padding: 15px 20px;
    flex: 1;
    overflow-y: auto;
    background: #fef7ff; /* Soft pink for the chat background */
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Message Styles */
.message {
    display: flex;
    align-items: flex-end;
    animation: fadeIn 0.3s ease;
}

.message.user {
    justify-content: flex-end;
}

.message.ai {
    justify-content: flex-start;
}

.message .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin: 0 10px;
}

.message .message-content {
    max-width: 70%;
    background-color: #ff3c83; /* Theme Pink for AI */
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    position: relative;
}

.message.user .message-content {
    background-color: #00c4cc; /* Theme Blue-Green for User */
}

.message .message-content::after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
}

.message.ai .message-content::after {
    left: -10px;
    border: 10px solid transparent;
    border-right-color: #ff3c83;
}

.message.user .message-content::after {
    right: -10px;
    border: 10px solid transparent;
    border-left-color: #00c4cc;
}

/* Timestamp Styles */
.timestamp {
    font-size: 0.75rem;
    color: #888;
    margin: 0 10px;
}

/* Chat Input Styles */
.chat-input {
    padding: 15px 20px;
    background-color: #ffeef2; /* Theme Soft Pink */
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-input textarea {
    flex: 1;
    padding: 10px 15px;
    border-radius: 20px;
    border: 1px solid #ddd;
    resize: none;
    font-size: 0.95rem;
}

.chat-input button {
    padding: 10px 20px;
    background-color: #ff3c83; /* Theme Pink */
    border: none;
    color: white;
    font-size: 0.95rem;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.chat-input button:hover {
    background-color: #e22f71;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .floating-chat-window {
        width: 90%;
        right: 5%;
    }

    .chat-input textarea {
        width: 60%;
    }
}
/* Header Styles */
header {
    position: relative;
    z-index: 1050;
  }
  
  .navbar {
    background: linear-gradient(90deg, #1d3557, #457b9d);
    transition: background 0.3s ease, box-shadow 0.3s ease;
  }
  
  .navbar-brand .logo-text {
    font-size: 1.8rem;
    font-weight: bold;
    background: linear-gradient(to right, #a8dadc, #f4a261);
    background-clip: text; /* Standard property */
    -webkit-background-clip: text; /* For WebKit-based browsers */
    -webkit-text-fill-color: transparent;
    animation: logoAnimation 5s linear infinite;
}
  
  @keyframes logoAnimation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  .navbar .nav-link {
    font-size: 1rem;
    font-weight: 500;
    color: #f1faee;
    transition: color 0.3s ease, transform 0.3s ease;
  }
  
  .navbar .nav-link:hover {
    color: #f4a261;
    transform: translateY(-3px);
  }
  
  .navbar .nav-link.active {
    font-weight: 700;
    color: #e63946;
  }
  
  .navbar-toggler {
    border: none;
    background: #e63946;
  }
  
  .navbar-toggler-icon {
    background-image: linear-gradient(to right, #1d3557, #457b9d);
  }
  
  .navbar.sticky {
    background: #1d3557;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  
  /* Sticky Navbar on Scroll */
  .sticky {
    position: fixed;
    top: 0;
    width: 100%;
  }
  
  /* Responsive Enhancements */
  @media (max-width: 768px) {
    .navbar .nav-link {
      font-size: 0.9rem;
    }
  
    .navbar-brand .logo-text {
      font-size: 1.5rem;
    }
  }