<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> - AI Portfolio</title>
  <meta name="description" content="<PERSON><PERSON><PERSON> Gupta AI Portfolio">
  <meta name="author" content="<PERSON>ks<PERSON>">

  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">

  <!-- Additional CSS Files -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/templatemo-digimedia-v1.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animated.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animate.min.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

  <!-- Custom Inline Styles for Extra Responsiveness -->
  <style>
    /* Adjust the floating chat window on smaller screens */
    @media (max-width: 768px) {
      .floating-chat-window {
        width: 90%;
        right: 5%;
      }
    }
  </style>
</head>

<body>

  <!-- ***** Header / Navbar ***** -->
  <header>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
      <div class="container">
        <a class="navbar-brand" href="#top">
          <span class="logo-text">Akshat Gupta</span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="#top">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#about">About</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#skills">Skills</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#projects">Projects</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#certifications">Certifications</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#contact">Contact</a>
            </li>
            <!-- Download Resume button with a distinct style -->
            <li class="nav-item">
              <a class="nav-link download-resume" href="{{ url_for('static', filename='images/resume.pdf') }}" download>Download Resume</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>

  <!-- ***** Main Banner Area ***** -->
  <div id="top" class="main-banner wow fadeIn" data-wow-duration="1s" data-wow-delay="0.5s">
    <div class="container">
      <div class="row align-items-center">
        <!-- Text Section -->
        <div class="col-lg-6">
          <h6 class="wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s" 
              style="text-transform: uppercase; color: #ffffff; font-size: 22px; font-weight: 700; letter-spacing: 2px;">
            Welcome to My Portfolio
          </h6>
          <h2>
            <span style="color: #ffffff;">I'm</span> 
            <span id="animated-name">
              <span>Akshat Gupta</span>
              <span>अक्षत गुप्ता</span>
            </span>
          </h2>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
            A passionate AI innovator, solving real-world challenges with cutting-edge technologies like machine learning, NLP, and more.
          </p>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
            Explore my portfolio to see the latest projects where I applied AI to drive real-world impact and bring innovation to various domains.
          </p>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.9s">
            <a href="#contact" class="btn btn-lg">Get In Touch</a>
          </p>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Main Banner ***** -->

  <!-- ***** About Section ***** -->
  <div id="about" class="about-section section">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <img src="{{ url_for('static', filename='images/profile_pic.png') }}" alt="About Me" 
               class="img-fluid wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.5s">
        </div>
        <div class="col-lg-6">
          <h2 class="wow fadeInRight" data-wow-duration="1s" data-wow-delay="0.3s">About Me</h2>
          <p class="wow fadeInRight" data-wow-duration="1s" data-wow-delay="0.6s">
            I am a Computer Science student specializing in Artificial Intelligence at Bennett University. My expertise includes machine learning, NLP, and generative AI. I thrive on solving real-world problems with technology.
          </p>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End About Section ***** -->
  <!-- ***** Academic Highlights Section ***** -->
<div id="academics" class="academics section">
  <div class="container">
    <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Academic Highlights</h2>
    <p class="text-center mb-5">An overview of my academic achievements.</p>
    <div class="row">
      <!-- B.Tech CGPA Card -->
      <div class="col-lg-4 col-md-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
        <div class="academic-card">
          <h4>B.Tech CGPA</h4>
          <p>8.64 / 10</p>
        </div>
      </div>
      <!-- Intermediate Score Card -->
      <div class="col-lg-4 col-md-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
        <div class="academic-card">
          <h4>Intermediate</h4>
          <p>89%</p>
        </div>
      </div>
      <!-- High School Score Card -->
      <div class="col-lg-4 col-md-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
        <div class="academic-card">
          <h4>High School</h4>
          <p>92%</p>
        </div>
      </div>
    </div>
  </div>
</div>
  <!-- ***** Skills Section ***** -->
  <div id="skills" class="skills section">
    <div class="container">
      <h2 class="text-center">My Skills</h2>
      <div class="row text-center">
        <!-- Skill Item 1 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.2s">
          <img src="https://cdn-icons-png.flaticon.com/512/5968/5968350.png" alt="Python" style="width: 70px; height: 70px;">
          <h4>Python</h4>
          <p>Proficient in Python for AI and ML development, including libraries like TensorFlow, NumPy, and Pandas.</p>
        </div>
        <!-- Skill Item 2 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
          <img src="https://cdn-icons-png.flaticon.com/512/2306/2306145.png" alt="Machine Learning" style="width: 70px; height: 70px;">
          <h4>Machine Learning</h4>
          <p>Experienced in developing predictive models using supervised and unsupervised learning techniques.</p>
        </div>
        <!-- Skill Item 3 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
          <img src="https://cdn-icons-png.flaticon.com/512/226/226777.png" alt="Deep Learning" style="width: 70px; height: 70px;">
          <h4>Deep Learning</h4>
          <p>Specialized in neural network architectures, including CNNs, RNNs, and GANs.</p>
        </div>
        <!-- Skill Item 4 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.8s">
          <img src="https://cdn-icons-png.flaticon.com/512/4144/4144850.png" alt="RAG Models" style="width: 70px; height: 70px;">
          <h4>RAG Models</h4>
          <p>Expert in Retrieval-Augmented Generation for intelligent AI systems.</p>
        </div>
        <!-- Skill Item 5 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="1s">
          <img src="https://cdn-icons-png.flaticon.com/512/5969/5969475.png" alt="Agentic AI" style="width: 70px; height: 70px;">
          <h4>Agentic AI</h4>
          <p>Designing autonomous agents to solve dynamic real-world problems.</p>
        </div>
        <!-- Skill Item 6 -->
        <div class="col-lg-4 col-md-6 mb-4 skill-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.2s">
          <img src="https://cdn-icons-png.flaticon.com/512/1904/1904465.png" alt="NLP" style="width: 70px; height: 70px;">
          <h4>NLP</h4>
          <p>Building intelligent systems for language understanding, text analysis, and conversational AI.</p>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Skills Section ***** -->

  <!-- ***** Projects Section ***** -->
  <div id="projects" class="projects section">
    <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">My Projects</h2>
      <p class="text-center mb-5">Here are some of my featured projects that showcase my skills and expertise.</p>
      <div class="row">
        <!-- Project 1 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/project1.png') }}" alt="Project 1" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>Akshar</h4>
                <p>A chat app that translates text and audio messages in real time, built using Flutter.</p>
                <div class="tech-stack">
                  <span class="badge">Flutter</span>
                  <span class="badge">Dart</span>
                  <span class="badge">AI</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
        <!-- Project 2 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/project2.png') }}" alt="Project 2" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>FaceCheck</h4>
                <p>A facial recognition-based attendance system integrated with a SQL database.</p>
                <div class="tech-stack">
                  <span class="badge">Python</span>
                  <span class="badge">OpenCV</span>
                  <span class="badge">SQL</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
        <!-- Project 3 -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="project-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <div class="project-card">
              <img src="{{ url_for('static', filename='images/project3.png') }}" alt="Project 3" class="project-image img-fluid">
              <div class="project-overlay">
                <h4>FacePay</h4>
                <p>An AI-based payment system using facial recognition for secure UPI transactions.</p>
                <div class="tech-stack">
                  <span class="badge">AI</span>
                  <span class="badge">Python</span>
                  <span class="badge">Facial Recognition</span>
                </div>
                <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="btn btn-outline-light btn-sm">View on GitHub</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Projects Section ***** -->

  <!-- ***** Certifications Section ***** -->
  <div id="certifications" class="certifications section">
    <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Certifications</h2>
      <div class="row text-center justify-content-center">
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
          <h5>Machine Learning by IBM</h5>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
          <h5>Deep Learning by Google</h5>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
          <h5>Networking by Coursera</h5>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Certifications Section ***** -->

  <!-- ***** Contact Section ***** -->
  <div id="contact" class="contact section">
    <div class="container">
      <h2 class="text-center wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">Contact Me</h2>
      <div class="row justify-content-center wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
        <div class="col-lg-6">
          <form action="mailto:<EMAIL>" method="post" enctype="text/plain">
            <div class="form-group mb-3">
              <input type="text" name="name" class="form-control" placeholder="Your Name" required>
            </div>
            <div class="form-group mb-3">
              <input type="email" name="email" class="form-control" placeholder="Your Email" required>
            </div>
            <div class="form-group mb-3">
              <textarea name="message" class="form-control" rows="5" placeholder="Your Message" required></textarea>
            </div>
            <div class="text-center">
              <button type="submit" class="btn btn-success btn-lg">Send Message</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Contact Section ***** -->

  <!-- Back-to-Top Button -->
  <button onclick="topFunction()" id="backToTopBtn" title="Go to top">&uarr;</button>

  <!-- Floating Chat Button -->
  <div class="floating-chat-btn">
    <button id="chat-toggle" class="btn">
      <img src="{{ url_for('static', filename='images/chat.svg') }}" alt="Chat Bot Icon" style="width: 24px; height: 24px;">
    </button>
  </div>

  <!-- Floating Chat Window -->
  <div id="chat-window" class="floating-chat-window d-flex flex-column">
    <div class="chat-header">
      <h5>AI Assistant</h5>
      <button id="close-chat" class="close-btn">&times;</button>
    </div>
    <div class="chat-body flex-grow-1" id="chat-body"></div>
    <div class="chat-input">
      <textarea id="user-input" placeholder="Ask me anything..." rows="2"></textarea>
      <button id="send-btn">Send</button>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="container text-center">
      <p class="wow fadeIn" data-wow-duration="1s" data-wow-delay="0.3s">
        2025 © Akshat Gupta. All Rights Reserved.
      </p>
      <div class="social-links mt-3">
        <a href="mailto:<EMAIL>" target="_blank" class="mx-2">
          <img src="{{ url_for('static', filename='images/mail.svg') }}" alt="Email Icon" style="width: 24px; height: 24px;">
        </a>
        <a href="https://github.com/Akshat-Gupta04" target="_blank" class="mx-2">
          <img src="{{ url_for('static', filename='images/github.svg') }}" alt="GitHub Icon" style="width: 24px; height: 24px;">
        </a>
        <a href="tel:+9306199632" class="mx-2">
          <img src="{{ url_for('static', filename='images/alternate-phone.svg') }}" alt="Phone Icon" style="width: 24px; height: 24px;">
        </a>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
  <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/owl-carousel.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animation.js') }}"></script>
  <script src="{{ url_for('static', filename='js/imagesloaded.js') }}"></script>
  <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animated-name.js') }}"></script>

  <script>
    // Toggle chat window visibility
    document.getElementById("chat-toggle").addEventListener("click", function() {
      document.getElementById("chat-window").classList.toggle("show");
    });
    // Close chat window
    document.getElementById("close-chat").addEventListener("click", function() {
      document.getElementById("chat-window").classList.remove("show");
    });
    // Handle sending message
    document.getElementById("send-btn").addEventListener("click", async function() {
      var userInput = document.getElementById("user-input").value;
      if (userInput.trim() !== "") {
        addMessage(userInput, "user");
        document.getElementById("user-input").value = "";
        addMessage("Thinking...", "ai");
        try {
          const response = await fetch('/ask', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ question: userInput })
          });
          const data = await response.json();
          if (data.response) {
            updateAIResponse(data.response);
          } else {
            updateAIResponse("Sorry, I couldn't understand that.");
          }
        } catch (error) {
          updateAIResponse("Sorry, I'm having trouble connecting to the server.");
          console.error("Error:", error);
        }
      }
    });

    // Add message to the chat
    function addMessage(message, sender) {
      var chatBody = document.getElementById("chat-body");
      var messageElement = document.createElement("div");
      messageElement.classList.add("message", sender);
      if (sender === "user") {
        messageElement.innerHTML = `
          <div class="message-content">${escapeHtml(message)}</div>
          <img src="{{ url_for('static', filename='images/person.svg') }}" alt="User Avatar" class="avatar">
        `;
      } else if (sender === "ai") {
        messageElement.innerHTML = `
          <img src="{{ url_for('static', filename='images/ai.svg') }}" alt="AI Avatar" class="avatar">
          <div class="message-content">${escapeHtml(message)}</div>
        `;
      }
      chatBody.appendChild(messageElement);
      chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Update the last AI message with the response
    function updateAIResponse(message) {
      var chatBody = document.getElementById("chat-body");
      var aiMessageElement = chatBody.querySelector(".message.ai:last-child");
      if (aiMessageElement) {
        aiMessageElement.querySelector(".message-content").textContent = message;
      }
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
      var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    // Handle "Enter" key for sending messages
    document.getElementById("user-input").addEventListener("keypress", function(e) {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        document.getElementById("send-btn").click();
      }
    });

    // Back-to-Top Button functionality
    var backToTopBtn = document.getElementById("backToTopBtn");
    window.onscroll = function() { scrollFunction(); };
    function scrollFunction() {
      if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
        backToTopBtn.style.display = "block";
      } else {
        backToTopBtn.style.display = "none";
      }
    }
    function topFunction() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  </script>

</body>
</html>