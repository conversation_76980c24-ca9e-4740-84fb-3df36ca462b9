/**
 * Neural Network Animation for Academics Section
 * Creates an interactive, animated neural network background
 */

document.addEventListener('DOMContentLoaded', function() {
  // Get the canvas element
  const canvas = document.getElementById('academicsNetworkCanvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  
  // Set canvas dimensions to match parent container
  function resizeCanvas() {
    const academicsSection = document.querySelector('.academics');
    if (academicsSection) {
      canvas.width = academicsSection.offsetWidth;
      canvas.height = academicsSection.offsetHeight;
    } else {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    }
  }
  
  // Call resize on load and window resize
  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);
  
  // Node class for neural network
  class Node {
    constructor(x, y) {
      this.x = x;
      this.y = y;
      this.radius = 1.5 + Math.random() * 2;
      this.vx = Math.random() * 0.2 - 0.1;
      this.vy = Math.random() * 0.2 - 0.1;
      this.color = `rgba(180, 44, 29, ${0.3 + Math.random() * 0.4})`;
      this.pulseSpeed = 0.01 + Math.random() * 0.02;
      this.pulseDirection = Math.random() > 0.5 ? 1 : -1;
      this.pulseAmount = 0;
    }
    
    update() {
      // Move the node
      this.x += this.vx;
      this.y += this.vy;
      
      // Bounce off edges
      if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
      if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
      
      // Pulse effect
      this.pulseAmount += this.pulseSpeed * this.pulseDirection;
      if (this.pulseAmount > 1 || this.pulseAmount < 0) {
        this.pulseDirection *= -1;
      }
    }
    
    draw() {
      const pulseRadius = this.radius * (1 + this.pulseAmount * 0.3);
      
      // Draw glow
      const gradient = ctx.createRadialGradient(
        this.x, this.y, 0,
        this.x, this.y, pulseRadius * 3
      );
      gradient.addColorStop(0, `rgba(180, 44, 29, ${0.2 * (1 + this.pulseAmount * 0.5)})`);
      gradient.addColorStop(1, 'rgba(180, 44, 29, 0)');
      
      ctx.beginPath();
      ctx.arc(this.x, this.y, pulseRadius * 3, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw node
      ctx.beginPath();
      ctx.arc(this.x, this.y, pulseRadius, 0, Math.PI * 2);
      ctx.fillStyle = this.color;
      ctx.fill();
    }
  }
  
  // Create nodes
  const nodeCount = Math.min(Math.floor(canvas.width * canvas.height / 20000), 40);
  const nodes = [];
  
  for (let i = 0; i < nodeCount; i++) {
    const x = Math.random() * canvas.width;
    const y = Math.random() * canvas.height;
    nodes.push(new Node(x, y));
  }
  
  // Draw connections between nodes
  function drawConnections() {
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const dx = nodes[i].x - nodes[j].x;
        const dy = nodes[i].y - nodes[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // Only connect nodes within a certain distance
        if (distance < 120) {
          ctx.beginPath();
          ctx.moveTo(nodes[i].x, nodes[i].y);
          ctx.lineTo(nodes[j].x, nodes[j].y);
          
          // Fade opacity based on distance
          const opacity = 1 - distance / 120;
          ctx.strokeStyle = `rgba(180, 44, 29, ${opacity * 0.15})`;
          ctx.lineWidth = 0.8;
          ctx.stroke();
        }
      }
    }
  }
  
  // Animation loop
  function animate() {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Update and draw nodes
    nodes.forEach(node => {
      node.update();
      node.draw();
    });
    
    // Draw connections
    drawConnections();
    
    // Continue animation
    requestAnimationFrame(animate);
  }
  
  // Start animation
  animate();
  
  // Add animation to timeline items
  const timelineItems = document.querySelectorAll('.timeline-item');
  
  function checkTimelineVisibility() {
    timelineItems.forEach(item => {
      const rect = item.getBoundingClientRect();
      const isVisible = (
        rect.top >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
      );
      
      if (isVisible) {
        item.classList.add('animate');
      }
    });
  }
  
  // Check on scroll
  window.addEventListener('scroll', checkTimelineVisibility);
  
  // Check once on load
  setTimeout(checkTimelineVisibility, 500);
});
