/**
 * Modern Splash Animation
 * A dynamic, interactive splash screen with particle effects, smooth animations,
 * and image selection functionality.
 */

class ModernSplash {
  constructor() {
    // Image paths
    this.images = [
      'static/images/me-pics/a2.png',
      'static/images/me-pics/a3.png',
      'static/images/me-pics/a4.png',
      'static/images/me-pics/a5.png',
      'static/images/me-pics/a6.png',
      'static/images/me-pics/a8.png',
      'static/images/me-pics/a9.png',
      'static/images/me-pics/a10.png',
      'static/images/me-pics/a11.png'
    ];

    // DOM Elements
    this.splash = document.getElementById('splash');
    this.particleField = document.getElementById('particleField');
    this.imageGallery = document.getElementById('imageGallery');
    this.imageContainer = document.getElementById('imageContainer');
    this.loadingProgress = document.getElementById('loadingProgress');

    // State
    this.loadedImages = 0;
    this.selectedImages = [];
    this.particles = [];
    this.isAnimating = false;

    // Initialize
    this.init();
  }

  /**
   * Initialize the splash screen
   */
  init() {
    // Create particles
    this.createParticles();

    // Preload images
    this.preloadImages();

    // Start animation sequence
    setTimeout(() => this.startSequence(), 500);
  }

  /**
   * Create floating particles
   */
  createParticles() {
    const particleCount = 30;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';

      // Random size between 3px and 8px
      const size = Math.random() * 5 + 3;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Random position
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;

      // Random animation delay
      const delay = Math.random() * 5;
      particle.style.animationDelay = `${delay}s`;

      // Add to DOM
      this.particleField.appendChild(particle);
      this.particles.push(particle);
    }
  }

  /**
   * Preload all images
   */
  preloadImages() {
    this.images.forEach((src, index) => {
      const img = document.createElement('img');
      img.src = src;
      img.style.display = 'none';

      img.onload = () => {
        this.loadedImages++;
        const progress = (this.loadedImages / this.images.length) * 100;
        this.loadingProgress.style.width = `${progress}%`;

        // Create gallery image once loaded
        this.createGalleryImage(src, index);

        // When all images are loaded
        if (this.loadedImages === this.images.length) {
          setTimeout(() => this.showGalleryImages(), 500);
        }
      };

      this.imageContainer.appendChild(img);
    });
  }

  /**
   * Create a gallery image
   */
  createGalleryImage(src, index) {
    const img = document.createElement('img');
    img.src = src;
    img.className = 'gallery-image';
    img.dataset.index = index;

    img.addEventListener('click', () => {
      if (this.isAnimating) return;

      // Toggle selection
      if (img.classList.contains('active')) {
        img.classList.remove('active');
        this.selectedImages = this.selectedImages.filter(i => i !== index);
      } else {
        // Only allow selecting up to 2 images
        if (this.selectedImages.length < 2) {
          img.classList.add('active');
          this.selectedImages.push(index);

          // If 2 images are selected, proceed
          if (this.selectedImages.length === 2) {
            setTimeout(() => this.finalizeSelection(), 800);
          }
        }
      }
    });

    this.imageGallery.appendChild(img);
  }

  /**
   * Show all gallery images with staggered animation
   */
  showGalleryImages() {
    const images = document.querySelectorAll('.gallery-image');

    images.forEach((img, index) => {
      setTimeout(() => {
        img.style.opacity = '1';
        img.style.transform = 'scale(1) translateY(0)';
      }, index * 100);
    });
  }

  /**
   * Start the animation sequence
   */
  startSequence() {
    // If no images are selected after 5 seconds, select random ones
    setTimeout(() => {
      if (this.selectedImages.length < 2 && !this.isAnimating) {
        this.selectRandomImages();
      }
    }, 5000);
  }

  /**
   * Select random images if user doesn't select
   */
  selectRandomImages() {
    // Clear any existing selections
    document.querySelectorAll('.gallery-image.active').forEach(img => {
      img.classList.remove('active');
    });

    this.selectedImages = [];

    // Select 2 random images
    const allImages = document.querySelectorAll('.gallery-image');
    const shuffled = [...allImages].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, 2);

    selected.forEach(img => {
      img.classList.add('active');
      this.selectedImages.push(parseInt(img.dataset.index));
    });

    // Proceed with selection
    setTimeout(() => this.finalizeSelection(), 800);
  }

  /**
   * Finalize the image selection and animate to positions
   */
  finalizeSelection() {
    if (this.isAnimating || this.selectedImages.length !== 2) return;
    this.isAnimating = true;

    // Get the selected image elements
    const selectedImgElements = this.selectedImages.map(index =>
      this.imageContainer.querySelectorAll('img')[index]
    );

    // Get target positions
    const mainBannerRect = document.getElementById('mainProfilePic').getBoundingClientRect();
    const aboutRect = document.getElementById('aboutProfilePic').getBoundingClientRect();

    // Create clones for animation
    const clone1 = selectedImgElements[0].cloneNode(true);
    const clone2 = selectedImgElements[1].cloneNode(true);

    [clone1, clone2].forEach((clone, i) => {
      // Style the clone
      clone.style.position = 'fixed';
      clone.style.width = '120px';
      clone.style.height = '120px';
      clone.style.borderRadius = '10px';
      clone.style.zIndex = '9990'; // Lower z-index to not interfere with navbar
      clone.style.transition = 'all 1.2s cubic-bezier(0.34, 1.56, 0.64, 1)';
      clone.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';

      // Get original position
      const originalImg = document.querySelectorAll('.gallery-image')[this.selectedImages[i]];
      const rect = originalImg.getBoundingClientRect();

      // Set initial position
      clone.style.top = `${rect.top}px`;
      clone.style.left = `${rect.left}px`;

      // Add to body
      document.body.appendChild(clone);

      // Animate to target position
      setTimeout(() => {
        const targetRect = i === 0 ? mainBannerRect : aboutRect;
        clone.style.top = `${targetRect.top}px`;
        clone.style.left = `${targetRect.left}px`;
        clone.style.width = `${targetRect.width}px`;
        clone.style.height = `${targetRect.height}px`;
        clone.style.borderRadius = '10px';
      }, 100);
    });

    // Set the actual profile images
    setTimeout(() => {
      document.getElementById('mainProfilePic').src = selectedImgElements[0].src;
      document.getElementById('aboutProfilePic').src = selectedImgElements[1].src;

      // Remove clones
      setTimeout(() => {
        document.body.removeChild(clone1);
        document.body.removeChild(clone2);

        // Fade out splash
        this.fadeOutSplash();
      }, 500);
    }, 1000);
  }

  /**
   * Fade out the splash screen
   */
  fadeOutSplash() {
    this.splash.style.transition = 'opacity 1s ease';
    this.splash.style.opacity = '0';

    setTimeout(() => {
      this.splash.style.display = 'none';
    }, 1000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ModernSplash();
});
