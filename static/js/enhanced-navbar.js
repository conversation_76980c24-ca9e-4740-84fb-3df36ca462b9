/**
 * Enhanced Navbar Functionality
 * Adds scroll effects, active link highlighting, and smooth animations
 */

document.addEventListener('DOMContentLoaded', function() {
  // Get navbar elements
  const navbar = document.querySelector('.navbar');
  const navbarToggler = document.querySelector('.navbar-toggler');
  const navbarCollapse = document.querySelector('.navbar-collapse');
  const navLinks = document.querySelectorAll('.nav-link');
  
  // Replace default toggler icon with custom one
  if (navbarToggler) {
    // Remove the default toggler icon
    const defaultIcon = navbarToggler.querySelector('.navbar-toggler-icon');
    if (defaultIcon) {
      defaultIcon.remove();
    }
    
    // Add custom toggler icon
    const customIcon = document.createElement('span');
    customIcon.className = 'toggler-icon';
    navbarToggler.appendChild(customIcon);
  }
  
  // Add icons to nav links on mobile
  function addNavIcons() {
    if (window.innerWidth <= 991) {
      navLinks.forEach(link => {
        // Skip if icon already exists
        if (link.querySelector('i')) return;
        
        const text = link.textContent.trim();
        let iconClass = '';
        
        // Determine icon based on link text
        switch(text) {
          case 'Home':
            iconClass = 'fas fa-home';
            break;
          case 'About':
            iconClass = 'fas fa-user';
            break;
          case 'Acadamics':
            iconClass = 'fas fa-graduation-cap';
            break;
          case 'Research':
            iconClass = 'fas fa-flask';
            break;
          case 'Skills':
            iconClass = 'fas fa-code';
            break;
          case 'Projects':
            iconClass = 'fas fa-project-diagram';
            break;
          case 'Certificates':
            iconClass = 'fas fa-certificate';
            break;
          case 'Contact':
            iconClass = 'fas fa-envelope';
            break;
          case 'Blog':
            iconClass = 'fas fa-blog';
            break;
          case 'Download Resume':
            iconClass = 'fas fa-file-download';
            break;
          default:
            iconClass = 'fas fa-link';
        }
        
        // Create icon element
        const icon = document.createElement('i');
        icon.className = iconClass;
        
        // Insert icon before text
        link.innerHTML = '';
        link.appendChild(icon);
        link.appendChild(document.createTextNode(' ' + text));
      });
    }
  }
  
  // Call once on load
  addNavIcons();
  
  // Call again on window resize
  window.addEventListener('resize', addNavIcons);
  
  // Add scroll event to change navbar appearance
  window.addEventListener('scroll', function() {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });
  
  // Auto-close mobile menu when a link is clicked
  navLinks.forEach(link => {
    link.addEventListener('click', function() {
      if (window.innerWidth <= 991 && navbarCollapse.classList.contains('show')) {
        navbarToggler.click();
      }
    });
  });
  
  // Highlight active section in navbar
  function highlightNavLink() {
    // Get current scroll position
    const scrollPosition = window.scrollY;
    
    // Get all sections
    const sections = document.querySelectorAll('section, div.main-banner');
    
    // Loop through sections to find the one in view
    sections.forEach(section => {
      const sectionTop = section.offsetTop - 100;
      const sectionHeight = section.offsetHeight;
      const sectionId = section.getAttribute('id');
      
      if (sectionId && scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        // Remove active class from all links
        navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.parentElement) {
            link.parentElement.classList.remove('active');
          }
        });
        
        // Add active class to current section link
        const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
        if (activeLink) {
          activeLink.classList.add('active');
          if (activeLink.parentElement) {
            activeLink.parentElement.classList.add('active');
          }
        }
      }
    });
  }
  
  // Call on scroll
  window.addEventListener('scroll', highlightNavLink);
  
  // Call once on load
  highlightNavLink();
});
