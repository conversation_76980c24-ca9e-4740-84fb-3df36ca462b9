/**
 * Formal Splash Animation
 * A professional, structured splash screen with formal animations and consistent motion
 */

class FormalSplash {
  constructor() {
    // Image paths
    this.images = [
      'static/images/me-pics/a2.png',
      'static/images/me-pics/a3.png',
      'static/images/me-pics/a4.png',
      'static/images/me-pics/a5.png',
      'static/images/me-pics/a6.png',
      'static/images/me-pics/a8.png',
      'static/images/me-pics/a9.png',
      'static/images/me-pics/a10.png',
      'static/images/me-pics/a11.png'
    ];

    // DOM Elements
    this.splash = document.getElementById('splash');
    this.canvas = document.getElementById('particleCanvas');
    this.ctx = this.canvas.getContext('2d');
    this.galleryContainer = document.getElementById('galleryContainer');
    this.imageContainer = document.getElementById('imageContainer');
    this.loaderFill = document.getElementById('loaderFill');
    this.loaderPercentage = document.getElementById('loaderPercentage');

    // State
    this.loadedImages = 0;
    this.selectedImages = [];
    this.particles = [];
    this.isAnimating = false;

    // Hide navbar during splash animation
    const navbar = document.querySelector('header nav');
    if (navbar) {
      navbar.style.visibility = 'hidden';
      navbar.style.opacity = '0';
    }

    // Initialize
    this.init();
  }

  /**
   * Initialize the splash screen
   */
  init() {
    // Set up canvas
    this.setupCanvas();

    // Create particles
    this.createParticles();

    // Start animation loop
    this.animate();

    // Preload images
    this.preloadImages();

    // Add resize handler for responsive layout
    this.setupResizeHandler();
  }

  /**
   * Set up resize handler to ensure responsive layout
   */
  setupResizeHandler() {
    // Throttle function to limit resize event firing
    let resizeTimeout;

    window.addEventListener('resize', () => {
      // Clear previous timeout
      clearTimeout(resizeTimeout);

      // Set new timeout
      resizeTimeout = setTimeout(() => {
        // Update gallery layout if it exists
        if (this.galleryContainer && this.galleryContainer.children.length > 0) {
          // Adjust gallery item positions based on new screen size
          const items = this.galleryContainer.querySelectorAll('.gallery-item');

          // Determine optimal grid layout based on screen size
          let columns = 3; // Default for larger screens

          // Adjust columns based on screen width
          if (window.innerWidth < 768) {
            columns = 3;
          } else if (window.innerWidth < 576) {
            columns = 2;
          }

          // Update positions
          items.forEach((item, index) => {
            const position = index % this.images.length;
            const row = Math.floor(position / columns);
            const col = position % columns;

            // Update animation delay for smooth wave effect
            const baseDelay = 0.2;
            const formalDelay = baseDelay + (row * 0.2) + (col * 0.1);

            item.style.animationDelay = `${formalDelay}s`;
          });
        }
      }, 200); // 200ms throttle
    });
  }

  /**
   * Set up the canvas for particle animation
   */
  setupCanvas() {
    // Set canvas dimensions
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;

    // Handle resize
    window.addEventListener('resize', () => {
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;
    });
  }

  /**
   * Create particles for the background with AI/ML theme
   */
  createParticles() {
    const particleCount = 100; // Increased count for more density

    for (let i = 0; i < particleCount; i++) {
      // Create particles in a neural network-like pattern
      // Distribute particles in layers to simulate neural network architecture
      const layer = Math.floor(Math.random() * 4); // 4 layers
      const nodesInLayer = Math.floor(particleCount / 4);
      const nodePosition = i % nodesInLayer;

      // Calculate position with structured randomness for neural network look
      const x = (this.canvas.width / 5) * (layer + 1) + (Math.random() * 80 - 40);
      const y = (this.canvas.height / nodesInLayer) * nodePosition + (Math.random() * 60 - 30);

      // Add pulse effect for more dynamic appearance
      const pulseSpeed = 0.02 + Math.random() * 0.03;
      const pulseDirection = Math.random() > 0.5 ? 1 : -1;

      this.particles.push({
        x: x,
        y: y,
        radius: 2 + Math.random() * 2, // Larger, more visible size
        color: `rgba(${180 + Math.random() * 30}, ${44 + Math.random() * 20}, ${29 + Math.random() * 10}, ${0.4 + Math.random() * 0.4})`, // More visible
        vx: Math.random() * 0.3 - 0.15, // Slightly faster movement
        vy: Math.random() * 0.3 - 0.15,
        sinOffset: Math.random() * Math.PI * 2,
        pulseAmount: 0,
        pulseSpeed: pulseSpeed,
        pulseDirection: pulseDirection,
        layer: layer // Track which layer this particle belongs to
      });
    }
  }

  /**
   * Animation loop for particles with AI/ML theme
   */
  animate() {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Update and draw particles
    this.particles.forEach(particle => {
      // Update position with neural network-like motion
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Add subtle sine wave motion for organic feel
      particle.y += Math.sin(Date.now() * 0.0005 + particle.sinOffset) * 0.1;

      // Update pulse effect
      if (particle.pulseAmount !== undefined) {
        particle.pulseAmount += particle.pulseSpeed * particle.pulseDirection;
        if (particle.pulseAmount > 1 || particle.pulseAmount < 0) {
          particle.pulseDirection *= -1;
        }
      }

      // Wrap around edges
      if (particle.x < 0) particle.x = this.canvas.width;
      if (particle.x > this.canvas.width) particle.x = 0;
      if (particle.y < 0) particle.y = this.canvas.height;
      if (particle.y > this.canvas.height) particle.y = 0;

      // Draw particle with glow effect for AI/ML theme
      const radius = particle.pulseAmount !== undefined ?
        particle.radius * (1 + (particle.pulseAmount * 0.3)) : particle.radius;

      // Draw glow
      const gradient = this.ctx.createRadialGradient(
        particle.x, particle.y, 0,
        particle.x, particle.y, radius * 3
      );
      gradient.addColorStop(0, `rgba(180, 44, 29, ${0.3 * (particle.pulseAmount !== undefined ? (1 + particle.pulseAmount * 0.5) : 1)})`);
      gradient.addColorStop(1, 'rgba(180, 44, 29, 0)');

      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, radius * 3, 0, Math.PI * 2);
      this.ctx.fillStyle = gradient;
      this.ctx.fill();

      // Draw particle
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, radius, 0, Math.PI * 2);
      this.ctx.fillStyle = particle.color;
      this.ctx.fill();
    });

    // Connect particles to simulate neural network connections
    this.connectParticles();

    // Continue animation loop
    requestAnimationFrame(() => this.animate());
  }

  /**
   * Connect particles to simulate neural network connections
   */
  connectParticles() {
    // Group particles by layer for neural network-like connections
    const layeredParticles = [[], [], [], []];

    // Sort particles into layers
    this.particles.forEach(particle => {
      if (particle.layer !== undefined) {
        layeredParticles[particle.layer].push(particle);
      }
    });

    // Connect particles between adjacent layers (feed-forward neural network style)
    for (let layer = 0; layer < layeredParticles.length - 1; layer++) {
      const currentLayer = layeredParticles[layer];
      const nextLayer = layeredParticles[layer + 1];

      // Connect each node in current layer to some nodes in the next layer
      currentLayer.forEach(particle1 => {
        // Connect to a subset of nodes in the next layer
        nextLayer.forEach(particle2 => {
          const dx = particle1.x - particle2.x;
          const dy = particle1.y - particle2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Only connect if within reasonable distance
          if (distance < 200) {
            // Calculate opacity based on distance and pulse amount
            const baseOpacity = 0.15 * (1 - distance / 200);
            const pulseEffect = (particle1.pulseAmount || 0) + (particle2.pulseAmount || 0);
            const opacity = baseOpacity * (1 + pulseEffect * 0.5);

            // Draw connection with gradient for data flow effect
            const gradient = this.ctx.createLinearGradient(
              particle1.x, particle1.y, particle2.x, particle2.y
            );
            gradient.addColorStop(0, `rgba(180, 44, 29, ${opacity})`);
            gradient.addColorStop(1, `rgba(180, 44, 29, ${opacity * 0.7})`);

            this.ctx.beginPath();
            this.ctx.strokeStyle = gradient;
            this.ctx.lineWidth = 1 + (pulseEffect * 0.5);
            this.ctx.moveTo(particle1.x, particle1.y);
            this.ctx.lineTo(particle2.x, particle2.y);
            this.ctx.stroke();
          }
        });
      });
    }

    // Also add some connections within the same layer for more complex network appearance
    layeredParticles.forEach(layer => {
      for (let i = 0; i < layer.length; i++) {
        for (let j = i + 1; j < Math.min(i + 3, layer.length); j++) {
          const particle1 = layer[i];
          const particle2 = layer[j];

          const dx = particle1.x - particle2.x;
          const dy = particle1.y - particle2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            const opacity = 0.05 * (1 - distance / 100);

            this.ctx.beginPath();
            this.ctx.strokeStyle = `rgba(180, 44, 29, ${opacity})`;
            this.ctx.lineWidth = 0.5;
            this.ctx.moveTo(particle1.x, particle1.y);
            this.ctx.lineTo(particle2.x, particle2.y);
            this.ctx.stroke();
          }
        }
      }
    });
  }

  /**
   * Preload all images
   */
  preloadImages() {
    this.images.forEach((src, index) => {
      const img = document.createElement('img');
      img.src = src;
      img.style.display = 'none';

      img.onload = () => {
        this.loadedImages++;
        const progress = (this.loadedImages / this.images.length) * 100;
        this.loaderFill.style.width = `${progress}%`;
        this.loaderPercentage.textContent = `${Math.round(progress)}%`;

        // Create gallery item once loaded
        this.createGalleryItem(src, index);

        // When all images are loaded
        if (this.loadedImages === this.images.length) {
          setTimeout(() => this.showGalleryItems(), 500);
        }
      };

      this.imageContainer.appendChild(img);
    });
  }

  /**
   * Create a gallery item with formal motion
   */
  createGalleryItem(src, index) {
    const item = document.createElement('div');
    item.className = 'gallery-item';
    item.dataset.index = index;

    // Create image wrapper for 3D effects
    const imgWrapper = document.createElement('div');
    imgWrapper.className = 'gallery-item-wrapper';

    const img = document.createElement('img');
    img.src = src;
    img.alt = `Profile Image ${index + 1}`;

    // Add reflection effect
    const reflection = document.createElement('div');
    reflection.className = 'gallery-reflection';

    // Determine optimal grid layout based on screen size
    let columns = 3; // Default for larger screens

    // Adjust columns based on screen width
    if (window.innerWidth < 768) {
      columns = 3;
    } else if (window.innerWidth < 576) {
      columns = 2;
    }

    // Calculate position in the grid to create a wave effect
    const position = index % this.images.length;
    const row = Math.floor(position / columns);
    const col = position % columns;

    // Create a formal wave pattern with consistent timing
    const baseDelay = 0.2;
    const baseDuration = 4; // Same duration for all items

    // Delay increases by row and column for wave effect
    const formalDelay = baseDelay + (row * 0.2) + (col * 0.1);

    item.style.animationDelay = `${formalDelay}s`;
    item.style.animationDuration = `${baseDuration}s`;
    item.classList.add('formal-motion');

    // Add to DOM
    imgWrapper.appendChild(img);
    imgWrapper.appendChild(reflection);
    item.appendChild(imgWrapper);

    // Click event
    item.addEventListener('click', () => {
      if (this.isAnimating) return;

      // Toggle selection
      if (item.classList.contains('active')) {
        item.classList.remove('active');
        imgWrapper.style.transform = '';
        this.selectedImages = this.selectedImages.filter(i => i !== index);
      } else {
        // Only allow selecting up to 2 images
        if (this.selectedImages.length < 2) {
          item.classList.add('active');
          this.selectedImages.push(index);

          // Add particle burst effect on selection
          this.createSelectionParticles(item);

          // If 2 images are selected, proceed
          if (this.selectedImages.length === 2) {
            setTimeout(() => this.finalizeSelection(), 800);
          }
        }
      }
    });

    this.galleryContainer.appendChild(item);
  }

  /**
   * Create particle burst effect on selection
   */
  createSelectionParticles(item) {
    const rect = item.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Create particles in a more formal, structured pattern
    const particleCount = 12; // Fewer particles for cleaner look

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'selection-particle';
      particle.style.position = 'fixed';
      particle.style.width = '4px'; // Consistent size
      particle.style.height = '4px';
      particle.style.backgroundColor = '#b42c1d';
      particle.style.borderRadius = '50%';
      particle.style.opacity = '0.7'; // Consistent opacity

      // Position in a circular pattern
      const angle = (i / particleCount) * Math.PI * 2;
      const distance = 10; // Consistent distance
      particle.style.left = `${centerX + Math.cos(angle) * distance}px`;
      particle.style.top = `${centerY + Math.sin(angle) * distance}px`;

      // Consistent animation
      const duration = 0.6;
      const outDistance = 40; // Consistent outward distance
      particle.style.transition = `all ${duration}s ease-out`;

      document.body.appendChild(particle);

      // Animate outward in a perfect circle
      setTimeout(() => {
        particle.style.transform = `translate(${Math.cos(angle) * outDistance}px, ${Math.sin(angle) * outDistance}px)`;
        particle.style.opacity = '0';
      }, 10);

      // Remove after animation
      setTimeout(() => {
        document.body.removeChild(particle);
      }, duration * 1000 + 10);
    }
  }

  /**
   * Show all gallery items with formal, structured animation
   */
  showGalleryItems() {
    const items = document.querySelectorAll('.gallery-item');

    // Reveal items in a structured wave pattern
    items.forEach((item, index) => {
      const position = index % 9;
      const row = Math.floor(position / 3);
      const col = position % 3;

      // Calculate delay based on position for a wave effect
      const delay = 100 + (row * 100) + (col * 50);

      setTimeout(() => {
        // Start with opacity 0 and scale down
        item.style.opacity = '0';
        item.style.transform = 'scale(0.9)';

        // Trigger animation
        setTimeout(() => {
          item.style.opacity = '1';
          item.style.transform = '';
        }, 50);
      }, delay);
    });

    // Select random images after showing gallery items
    setTimeout(() => {
      if (!this.isAnimating) {
        this.selectRandomImages();
      }
    }, 2000);
  }

  /**
   * Select random images automatically
   */
  selectRandomImages() {
    // Clear any existing selections
    document.querySelectorAll('.gallery-item.active').forEach(item => {
      item.classList.remove('active');
    });

    this.selectedImages = [];

    // Select 2 random images
    const allItems = document.querySelectorAll('.gallery-item');
    const shuffled = [...allItems].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, 2);

    // Select images with a slight delay between them for a more formal effect
    selected.forEach((item, i) => {
      setTimeout(() => {
        item.classList.add('active');
        this.selectedImages.push(parseInt(item.dataset.index));
        this.createSelectionParticles(item);

        // Only proceed after both are selected
        if (i === 1) {
          setTimeout(() => this.finalizeSelection(), 800);
        }
      }, i * 300); // Delay between selections
    });
  }

  /**
   * Finalize the image selection and animate to positions
   */
  finalizeSelection() {
    if (this.isAnimating || this.selectedImages.length !== 2) return;
    this.isAnimating = true;

    // Get the selected image elements
    const selectedImgElements = this.selectedImages.map(index =>
      this.imageContainer.querySelectorAll('img')[index]
    );

    // Get target positions
    const mainBannerRect = document.getElementById('mainProfilePic').getBoundingClientRect();
    const aboutRect = document.getElementById('aboutProfilePic').getBoundingClientRect();

    // Create clones for animation
    const clone1 = selectedImgElements[0].cloneNode(true);
    const clone2 = selectedImgElements[1].cloneNode(true);

    [clone1, clone2].forEach((clone, i) => {
      // Get computed size from CSS variable
      const computedStyle = getComputedStyle(document.documentElement);
      const itemSize = computedStyle.getPropertyValue('--circle-img-size').trim();

      // Style the clone
      clone.style.position = 'fixed';
      clone.style.width = itemSize || 'min(140px, 15vw)'; // Use CSS var or fallback
      clone.style.height = itemSize || 'min(140px, 15vw)';
      clone.style.borderRadius = '10px';
      clone.style.zIndex = '9990';
      clone.style.transition = 'all 1.2s cubic-bezier(0.22, 1, 0.36, 1)'; // More formal easing
      clone.style.boxShadow = '0 15px 30px rgba(180, 44, 29, 0.4)';

      // Get original position
      const originalItem = document.querySelectorAll('.gallery-item')[this.selectedImages[i]];
      const originalImg = originalItem.querySelector('img');
      const rect = originalImg.getBoundingClientRect();

      // Set initial position
      clone.style.top = `${rect.top}px`;
      clone.style.left = `${rect.left}px`;

      // Add to body
      document.body.appendChild(clone);

      // Animate to target position with sequential timing
      setTimeout(() => {
        const targetRect = i === 0 ? mainBannerRect : aboutRect;

        // Create formal trail effect
        this.createFormalTrail(clone, targetRect);

        // Animate to target with a straight path
        clone.style.top = `${targetRect.top}px`;
        clone.style.left = `${targetRect.left}px`;
        clone.style.width = `${targetRect.width}px`;
        clone.style.height = `${targetRect.height}px`;
        clone.style.borderRadius = '10px';
      }, i * 400 + 200); // Sequential timing
    });

    // Set the actual profile images
    setTimeout(() => {
      document.getElementById('mainProfilePic').src = selectedImgElements[0].src;
      document.getElementById('aboutProfilePic').src = selectedImgElements[1].src;

      // Remove clones
      setTimeout(() => {
        document.body.removeChild(clone1);
        document.body.removeChild(clone2);

        // Fade out splash with final effect
        this.createFormalFinalEffect();
      }, 800);
    }, 2000);
  }

  /**
   * Create formal trail effect for image animation
   */
  createFormalTrail(clone, targetRect) {
    const startRect = clone.getBoundingClientRect();
    const startX = startRect.left + startRect.width / 2;
    const startY = startRect.top + startRect.height / 2;
    const endX = targetRect.left + targetRect.width / 2;
    const endY = targetRect.top + targetRect.height / 2;

    // Create trail with consistent spacing
    const trailCount = 8; // Fewer, more consistent trail elements

    for (let i = 0; i < trailCount; i++) {
      setTimeout(() => {
        const trailClone = clone.cloneNode(true);
        trailClone.style.position = 'fixed';
        trailClone.style.zIndex = '9980';
        trailClone.style.opacity = '0.25'; // Consistent opacity
        trailClone.style.transform = 'scale(0.85)'; // Consistent scale
        trailClone.style.filter = 'blur(3px)'; // Consistent blur
        trailClone.style.transition = 'all 0.7s ease-out';

        // Calculate position along a straight line
        const progress = i / (trailCount - 1);
        const posX = startX + (endX - startX) * progress;
        const posY = startY + (endY - startY) * progress;

        // Position the trail element
        trailClone.style.left = `${posX - startRect.width / 2}px`;
        trailClone.style.top = `${posY - startRect.height / 2}px`;

        document.body.appendChild(trailClone);

        // Fade out
        setTimeout(() => {
          trailClone.style.opacity = '0';

          // Remove after animation
          setTimeout(() => {
            document.body.removeChild(trailClone);
          }, 700);
        }, 100);
      }, i * 60); // Consistent timing
    }
  }

  /**
   * Create formal final effect before fading out
   */
  createFormalFinalEffect() {
    // Create a fade effect
    const fade = document.createElement('div');
    fade.style.position = 'fixed';
    fade.style.top = '0';
    fade.style.left = '0';
    fade.style.width = '100%';
    fade.style.height = '100%';
    fade.style.backgroundColor = 'rgba(0, 0, 0, 0.3)'; // Darker fade for more formal look
    fade.style.zIndex = '9997';
    fade.style.opacity = '0';
    fade.style.transition = 'opacity 0.8s ease'; // Slower transition

    document.body.appendChild(fade);

    // Fade in and out
    setTimeout(() => {
      fade.style.opacity = '1';

      setTimeout(() => {
        fade.style.opacity = '0';

        // Remove fade and fade out splash
        setTimeout(() => {
          document.body.removeChild(fade);
          this.fadeOutSplash();
        }, 800);
      }, 400);
    }, 200);
  }

  /**
   * Fade out the splash screen and scroll to top
   */
  fadeOutSplash() {
    this.splash.style.transition = 'opacity 0.8s ease'; // Reduced from 1.2s to 0.8s
    this.splash.style.opacity = '0';

    // Show navbar when splash animation ends
    const navbar = document.querySelector('header nav');
    if (navbar) {
      navbar.style.visibility = 'visible';
      navbar.style.opacity = '1';
    }

    // Scroll to top of the page (main banner)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    setTimeout(() => {
      this.splash.style.display = 'none';
    }, 800); // Reduced from 1200ms to 800ms
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FormalSplash();
});
