/**
 * Premium Splash Animation
 * A high-end, immersive splash screen with 3D effects, dynamic animations, and interactive elements
 */

class PremiumSplash {
  constructor() {
    // Image paths
    this.images = [
      'static/images/me-pics/a2.png',
      'static/images/me-pics/a3.png',
      'static/images/me-pics/a4.png',
      'static/images/me-pics/a5.png',
      'static/images/me-pics/a6.png',
      'static/images/me-pics/a8.png',
      'static/images/me-pics/a9.png',
      'static/images/me-pics/a10.png',
      'static/images/me-pics/a11.png'
    ];

    // DOM Elements
    this.splash = document.getElementById('splash');
    this.canvas = document.getElementById('particleCanvas');
    this.ctx = this.canvas.getContext('2d');
    this.galleryContainer = document.getElementById('galleryContainer');
    this.imageContainer = document.getElementById('imageContainer');
    this.loaderFill = document.getElementById('loaderFill');
    this.loaderPercentage = document.getElementById('loaderPercentage');

    // State
    this.loadedImages = 0;
    this.selectedImages = [];
    this.particles = [];
    this.isAnimating = false;
    this.mouseX = 0;
    this.mouseY = 0;

    // Initialize
    this.init();
  }

  /**
   * Initialize the splash screen
   */
  init() {
    // Set up canvas
    this.setupCanvas();

    // Create particles
    this.createParticles();

    // Start animation loop
    this.animate();

    // Track mouse movement for 3D effects
    this.trackMouseMovement();

    // Preload images
    this.preloadImages();
  }

  /**
   * Set up the canvas for particle animation
   */
  setupCanvas() {
    // Set canvas dimensions
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;

    // Handle resize
    window.addEventListener('resize', () => {
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;
    });
  }

  /**
   * Create particles for the background
   */
  createParticles() {
    const particleCount = 100;

    for (let i = 0; i < particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        radius: Math.random() * 2 + 1,
        color: `rgba(${180 + Math.random() * 75}, ${44 + Math.random() * 50}, ${29 + Math.random() * 30}, ${Math.random() * 0.5 + 0.1})`,
        vx: Math.random() * 0.5 - 0.25,
        vy: Math.random() * 0.5 - 0.25,
        sinOffset: Math.random() * Math.PI * 2
      });
    }
  }

  /**
   * Animation loop for particles
   */
  animate() {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Update and draw particles
    this.particles.forEach(particle => {
      // Update position with sine wave motion
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.y += Math.sin(Date.now() * 0.001 + particle.sinOffset) * 0.2;

      // Wrap around edges
      if (particle.x < 0) particle.x = this.canvas.width;
      if (particle.x > this.canvas.width) particle.x = 0;
      if (particle.y < 0) particle.y = this.canvas.height;
      if (particle.y > this.canvas.height) particle.y = 0;

      // Draw particle
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      this.ctx.fillStyle = particle.color;
      this.ctx.fill();
    });

    // Connect nearby particles with lines
    this.connectParticles();

    // Continue animation loop
    requestAnimationFrame(() => this.animate());
  }

  /**
   * Connect nearby particles with lines
   */
  connectParticles() {
    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const dx = this.particles[i].x - this.particles[j].x;
        const dy = this.particles[i].y - this.particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          this.ctx.beginPath();
          this.ctx.strokeStyle = `rgba(180, 44, 29, ${0.1 * (1 - distance / 100)})`;
          this.ctx.lineWidth = 0.5;
          this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
          this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
          this.ctx.stroke();
        }
      }
    }
  }

  /**
   * Track mouse movement for 3D effects
   */
  trackMouseMovement() {
    document.addEventListener('mousemove', (e) => {
      this.mouseX = e.clientX;
      this.mouseY = e.clientY;

      // Calculate mouse position relative to center
      const centerX = window.innerWidth / 2;
      const centerY = window.innerHeight / 2;
      const deltaX = (this.mouseX - centerX) / centerX;
      const deltaY = (this.mouseY - centerY) / centerY;

      // Apply 3D rotation to gallery items
      const galleryItems = document.querySelectorAll('.gallery-item');
      galleryItems.forEach(item => {
        if (!item.classList.contains('active')) {
          const rotateX = -deltaY * 10;
          const rotateY = deltaX * 10;
          item.style.transform = `translateZ(10px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        }
      });
    });
  }

  /**
   * Preload all images
   */
  preloadImages() {
    this.images.forEach((src, index) => {
      const img = document.createElement('img');
      img.src = src;
      img.style.display = 'none';

      img.onload = () => {
        this.loadedImages++;
        const progress = (this.loadedImages / this.images.length) * 100;
        this.loaderFill.style.width = `${progress}%`;
        this.loaderPercentage.textContent = `${Math.round(progress)}%`;

        // Create gallery item once loaded
        this.createGalleryItem(src, index);

        // When all images are loaded
        if (this.loadedImages === this.images.length) {
          setTimeout(() => this.showGalleryItems(), 500);
        }
      };

      this.imageContainer.appendChild(img);
    });
  }

  /**
   * Create a gallery item
   */
  createGalleryItem(src, index) {
    const item = document.createElement('div');
    item.className = 'gallery-item';
    item.dataset.index = index;

    const img = document.createElement('img');
    img.src = src;
    img.alt = `Profile Image ${index + 1}`;

    item.appendChild(img);

    item.addEventListener('click', () => {
      if (this.isAnimating) return;

      // Toggle selection
      if (item.classList.contains('active')) {
        item.classList.remove('active');
        this.selectedImages = this.selectedImages.filter(i => i !== index);
      } else {
        // Only allow selecting up to 2 images
        if (this.selectedImages.length < 2) {
          item.classList.add('active');
          this.selectedImages.push(index);

          // Add particle burst effect on selection
          this.createSelectionParticles(item);

          // If 2 images are selected, proceed
          if (this.selectedImages.length === 2) {
            setTimeout(() => this.finalizeSelection(), 800);
          }
        }
      }
    });

    this.galleryContainer.appendChild(item);
  }

  /**
   * Create particle burst effect on selection
   */
  createSelectionParticles(item) {
    const rect = item.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 20; i++) {
      const particle = document.createElement('div');
      particle.className = 'selection-particle';
      particle.style.position = 'fixed';
      particle.style.width = `${Math.random() * 6 + 2}px`;
      particle.style.height = particle.style.width;
      particle.style.backgroundColor = '#b42c1d';
      particle.style.borderRadius = '50%';
      particle.style.opacity = Math.random() * 0.5 + 0.5;

      // Random position around center
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 20;
      particle.style.left = `${centerX + Math.cos(angle) * distance}px`;
      particle.style.top = `${centerY + Math.sin(angle) * distance}px`;

      // Random animation
      const duration = Math.random() * 0.5 + 0.5;
      const distance2 = Math.random() * 50 + 20;
      particle.style.transition = `all ${duration}s ease-out`;

      document.body.appendChild(particle);

      // Animate outward
      setTimeout(() => {
        particle.style.transform = `translate(${Math.cos(angle) * distance2}px, ${Math.sin(angle) * distance2}px)`;
        particle.style.opacity = '0';
      }, 10);

      // Remove after animation
      setTimeout(() => {
        document.body.removeChild(particle);
      }, duration * 1000 + 10);
    }
  }

  /**
   * Show all gallery items with staggered animation
   */
  showGalleryItems() {
    const items = document.querySelectorAll('.gallery-item');

    items.forEach((item, index) => {
      setTimeout(() => {
        item.style.opacity = '1';
        item.style.transform = 'translateZ(10px)';
      }, index * 100);
    });

    // Select pre-chosen images immediately after showing gallery items
    setTimeout(() => {
      if (!this.isAnimating) {
        this.selectRandomImages();
      }
    }, 1500); // Shorter delay for a more immediate experience
  }

  /**
   * Select random images automatically
   */
  selectRandomImages() {
    // Clear any existing selections
    document.querySelectorAll('.gallery-item.active').forEach(item => {
      item.classList.remove('active');
    });

    this.selectedImages = [];

    // Select 2 random images
    const allItems = document.querySelectorAll('.gallery-item');
    const shuffled = [...allItems].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, 2);

    selected.forEach(item => {
      item.classList.add('active');
      this.selectedImages.push(parseInt(item.dataset.index));
      this.createSelectionParticles(item);
    });

    // Proceed with selection
    setTimeout(() => this.finalizeSelection(), 800);
  }

  /**
   * Finalize the image selection and animate to positions
   */
  finalizeSelection() {
    if (this.isAnimating || this.selectedImages.length !== 2) return;
    this.isAnimating = true;

    // Get the selected image elements
    const selectedImgElements = this.selectedImages.map(index =>
      this.imageContainer.querySelectorAll('img')[index]
    );

    // Get target positions
    const mainBannerRect = document.getElementById('mainProfilePic').getBoundingClientRect();
    const aboutRect = document.getElementById('aboutProfilePic').getBoundingClientRect();

    // Create clones for animation
    const clone1 = selectedImgElements[0].cloneNode(true);
    const clone2 = selectedImgElements[1].cloneNode(true);

    [clone1, clone2].forEach((clone, i) => {
      // Style the clone
      clone.style.position = 'fixed';
      clone.style.width = '140px';
      clone.style.height = '140px';
      clone.style.borderRadius = '10px';
      clone.style.zIndex = '9990'; // Lower z-index to not interfere with navbar
      clone.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
      clone.style.boxShadow = '0 20px 50px rgba(180, 44, 29, 0.5)';

      // Get original position
      const originalItem = document.querySelectorAll('.gallery-item')[this.selectedImages[i]];
      const originalImg = originalItem.querySelector('img');
      const rect = originalImg.getBoundingClientRect();

      // Set initial position
      clone.style.top = `${rect.top}px`;
      clone.style.left = `${rect.left}px`;

      // Add to body
      document.body.appendChild(clone);

      // Animate to target position with delay between images
      setTimeout(() => {
        const targetRect = i === 0 ? mainBannerRect : aboutRect;

        // Create trail effect
        this.createImageTrail(clone, targetRect);

        // Animate to target
        clone.style.top = `${targetRect.top}px`;
        clone.style.left = `${targetRect.left}px`;
        clone.style.width = `${targetRect.width}px`;
        clone.style.height = `${targetRect.height}px`;
        clone.style.borderRadius = '10px';
      }, i * 200 + 100);
    });

    // Set the actual profile images
    setTimeout(() => {
      document.getElementById('mainProfilePic').src = selectedImgElements[0].src;
      document.getElementById('aboutProfilePic').src = selectedImgElements[1].src;

      // Remove clones
      setTimeout(() => {
        document.body.removeChild(clone1);
        document.body.removeChild(clone2);

        // Fade out splash with final effect
        this.createFinalEffect();
      }, 800);
    }, 1500);
  }

  /**
   * Create trail effect for image animation
   */
  createImageTrail(clone, targetRect) {
    const startRect = clone.getBoundingClientRect();
    const startX = startRect.left + startRect.width / 2;
    const startY = startRect.top + startRect.height / 2;
    const endX = targetRect.left + targetRect.width / 2;
    const endY = targetRect.top + targetRect.height / 2;

    // Create trail particles
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        const trailClone = clone.cloneNode(true);
        trailClone.style.position = 'fixed';
        trailClone.style.zIndex = '9980';
        trailClone.style.opacity = '0.3';
        trailClone.style.transform = 'scale(0.8)';
        trailClone.style.filter = 'blur(5px)';
        trailClone.style.transition = 'all 0.8s ease-out';

        // Calculate position along the path
        const progress = i / 10;
        const posX = startX + (endX - startX) * progress;
        const posY = startY + (endY - startY) * progress;

        // Position the trail element
        trailClone.style.left = `${posX - startRect.width / 2}px`;
        trailClone.style.top = `${posY - startRect.height / 2}px`;

        document.body.appendChild(trailClone);

        // Fade out
        setTimeout(() => {
          trailClone.style.opacity = '0';
          trailClone.style.transform = 'scale(0.5)';

          // Remove after animation
          setTimeout(() => {
            document.body.removeChild(trailClone);
          }, 800);
        }, 100);
      }, i * 50);
    }
  }

  /**
   * Create final effect before fading out
   */
  createFinalEffect() {
    // Create a flash effect
    const flash = document.createElement('div');
    flash.style.position = 'fixed';
    flash.style.top = '0';
    flash.style.left = '0';
    flash.style.width = '100%';
    flash.style.height = '100%';
    flash.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
    flash.style.zIndex = '9997';
    flash.style.opacity = '0';
    flash.style.transition = 'opacity 0.5s ease';

    document.body.appendChild(flash);

    // Flash in and out
    setTimeout(() => {
      flash.style.opacity = '1';

      setTimeout(() => {
        flash.style.opacity = '0';

        // Remove flash and fade out splash
        setTimeout(() => {
          document.body.removeChild(flash);
          this.fadeOutSplash();
        }, 500);
      }, 200);
    }, 100);
  }

  /**
   * Fade out the splash screen
   */
  fadeOutSplash() {
    this.splash.style.transition = 'opacity 1s ease';
    this.splash.style.opacity = '0';

    setTimeout(() => {
      this.splash.style.display = 'none';
    }, 1000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PremiumSplash();
});
