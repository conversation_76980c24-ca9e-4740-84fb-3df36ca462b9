/**
 * Neural Network Animation for Skills Section
 * Creates an interactive, animated neural network background
 */

document.addEventListener('DOMContentLoaded', function() {
  // Get the canvas element
  const canvas = document.getElementById('skillsNetworkCanvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  
  // Set canvas dimensions to match parent container
  function resizeCanvas() {
    const skillsSection = document.querySelector('.skills');
    if (skillsSection) {
      canvas.width = skillsSection.offsetWidth;
      canvas.height = skillsSection.offsetHeight;
    } else {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    }
  }
  
  // Call resize on load and window resize
  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);
  
  // Node class for neural network
  class Node {
    constructor(x, y) {
      this.x = x;
      this.y = y;
      this.radius = 2 + Math.random() * 2;
      this.vx = Math.random() * 0.3 - 0.15;
      this.vy = Math.random() * 0.3 - 0.15;
      this.color = `rgba(180, 44, 29, ${0.3 + Math.random() * 0.4})`;
      this.pulseSpeed = 0.02 + Math.random() * 0.03;
      this.pulseDirection = Math.random() > 0.5 ? 1 : -1;
      this.pulseAmount = 0;
    }
    
    update() {
      // Move the node
      this.x += this.vx;
      this.y += this.vy;
      
      // Bounce off edges
      if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
      if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
      
      // Pulse effect
      this.pulseAmount += this.pulseSpeed * this.pulseDirection;
      if (this.pulseAmount > 1 || this.pulseAmount < 0) {
        this.pulseDirection *= -1;
      }
    }
    
    draw() {
      const pulseRadius = this.radius * (1 + this.pulseAmount * 0.3);
      
      // Draw glow
      const gradient = ctx.createRadialGradient(
        this.x, this.y, 0,
        this.x, this.y, pulseRadius * 3
      );
      gradient.addColorStop(0, `rgba(180, 44, 29, ${0.3 * (1 + this.pulseAmount * 0.5)})`);
      gradient.addColorStop(1, 'rgba(180, 44, 29, 0)');
      
      ctx.beginPath();
      ctx.arc(this.x, this.y, pulseRadius * 3, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw node
      ctx.beginPath();
      ctx.arc(this.x, this.y, pulseRadius, 0, Math.PI * 2);
      ctx.fillStyle = this.color;
      ctx.fill();
    }
  }
  
  // Create nodes
  const nodeCount = Math.min(Math.floor(canvas.width * canvas.height / 15000), 50);
  const nodes = [];
  
  for (let i = 0; i < nodeCount; i++) {
    const x = Math.random() * canvas.width;
    const y = Math.random() * canvas.height;
    nodes.push(new Node(x, y));
  }
  
  // Draw connections between nodes
  function drawConnections() {
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const dx = nodes[i].x - nodes[j].x;
        const dy = nodes[i].y - nodes[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // Only connect nodes within a certain distance
        if (distance < 150) {
          ctx.beginPath();
          ctx.moveTo(nodes[i].x, nodes[i].y);
          ctx.lineTo(nodes[j].x, nodes[j].y);
          
          // Fade opacity based on distance
          const opacity = 1 - distance / 150;
          ctx.strokeStyle = `rgba(180, 44, 29, ${opacity * 0.2})`;
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      }
    }
  }
  
  // Animation loop
  function animate() {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Update and draw nodes
    nodes.forEach(node => {
      node.update();
      node.draw();
    });
    
    // Draw connections
    drawConnections();
    
    // Continue animation
    requestAnimationFrame(animate);
  }
  
  // Start animation
  animate();
  
  // Skills category filtering
  const categoryButtons = document.querySelectorAll('.skill-category');
  const skillCards = document.querySelectorAll('.advanced-skill-card');
  
  if (categoryButtons.length > 0) {
    categoryButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Remove active class from all buttons
        categoryButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        this.classList.add('active');
        
        // Get category to filter
        const category = this.getAttribute('data-category');
        
        // Show all cards if "All" is selected
        if (category === 'all') {
          skillCards.forEach(card => {
            card.style.display = 'flex';
            setTimeout(() => {
              card.style.opacity = '1';
              card.style.transform = 'translateY(0)';
            }, 50);
          });
        } else {
          // Filter cards by category
          skillCards.forEach(card => {
            if (card.getAttribute('data-category') === category) {
              card.style.display = 'flex';
              setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
              }, 50);
            } else {
              card.style.opacity = '0';
              card.style.transform = 'translateY(20px)';
              setTimeout(() => {
                card.style.display = 'none';
              }, 300);
            }
          });
        }
      });
    });
  }
});
