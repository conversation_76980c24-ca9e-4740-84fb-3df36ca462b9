// Modern Projects Slider for Mobile Devices
document.addEventListener('DOMContentLoaded', function() {
  // Get slider elements
  const slider = document.getElementById('projectsSlider');
  const slides = document.querySelectorAll('.slider-slide');
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');
  const dotsContainer = document.getElementById('sliderDots');

  // Only initialize if slider exists (mobile view)
  if (!slider) return;

  // Slider state
  let currentIndex = 0;
  const totalSlides = slides.length;

  // Create dots
  for (let i = 0; i < totalSlides; i++) {
    const dot = document.createElement('div');
    dot.classList.add('slider-dot');
    if (i === 0) dot.classList.add('active');
    dot.setAttribute('data-index', i);
    dot.addEventListener('click', () => {
      goToSlide(i);
    });
    dotsContainer.appendChild(dot);
  }

  // Set initial active slide
  updateSlider();

  // Event listeners for buttons
  prevBtn.addEventListener('click', () => {
    goToSlide(currentIndex - 1);
  });

  nextBtn.addEventListener('click', () => {
    goToSlide(currentIndex + 1);
  });

  // Touch events for swipe
  let touchStartX = 0;
  let touchEndX = 0;

  slider.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
  });

  slider.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    if (touchEndX < touchStartX - swipeThreshold) {
      // Swipe left - next slide
      goToSlide(currentIndex + 1);
    } else if (touchEndX > touchStartX + swipeThreshold) {
      // Swipe right - previous slide
      goToSlide(currentIndex - 1);
    }
  }

  // Go to specific slide
  function goToSlide(index) {
    // Handle circular navigation
    if (index < 0) {
      index = totalSlides - 1;
    } else if (index >= totalSlides) {
      index = 0;
    }

    currentIndex = index;
    updateSlider();
  }

  // Update slider position and active states
  function updateSlider() {
    // Calculate slide width including margins
    const slideWidth = slides[0].offsetWidth + 20; // 20px for margins

    // Update transform
    slider.style.transform = `translateX(-${currentIndex * slideWidth}px)`;

    // Update active classes
    slides.forEach((slide, i) => {
      slide.classList.toggle('active', i === currentIndex);
    });

    // Update dots
    const dots = document.querySelectorAll('.slider-dot');
    dots.forEach((dot, i) => {
      dot.classList.toggle('active', i === currentIndex);
    });
  }

  // Auto-advance slider every 5 seconds
  let autoSlideInterval = setInterval(() => {
    goToSlide(currentIndex + 1);
  }, 5000);

  // Pause auto-advance on interaction
  slider.addEventListener('mouseenter', () => {
    clearInterval(autoSlideInterval);
  });

  slider.addEventListener('mouseleave', () => {
    autoSlideInterval = setInterval(() => {
      goToSlide(currentIndex + 1);
    }, 5000);
  });

  // Handle window resize
  window.addEventListener('resize', updateSlider);
});