// Particle System Class
class ParticleSystem {
  constructor() {
    this.canvas = document.getElementById('particleCanvas');
    if (!this.canvas) return;
    
    this.ctx = this.canvas.getContext('2d');
    this.particles = [];
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
    
    this.initParticles();
    this.animate();
  }

  initParticles() {
    for (let i = 0; i < 100; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        radius: Math.random() * 2 + 1,
        vx: Math.random() * 2 - 1,
        vy: Math.random() * 2 - 1
      });
    }
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
      if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;
      
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      this.ctx.fill();
    });
    
    requestAnimationFrame(() => this.animate());
  }
}

// Morphing Logo Class
class MorphingLogo {
  constructor() {
    this.svg = document.getElementById('morphPath');
    if (!this.svg) return;
    
    this.paths = [
      "M100,100 L400,100 L400,400 L100,400 Z", // Square
      "M250,100 L400,250 L250,400 L100,250 Z"  // Diamond
    ];
    
    this.currentPath = 0;
    this.morphInterval = setInterval(() => this.morph(), 3000);
  }

  morph() {
    const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
    path.setAttribute("d", this.paths[this.currentPath]);
    path.setAttribute("fill", "none");
    path.setAttribute("stroke", "white");
    
    this.svg.innerHTML = '';
    this.svg.appendChild(path);
    
    this.currentPath = (this.currentPath + 1) % this.paths.length;
  }
}

// Typing Effect Class
class TypingEffect {
  constructor() {
    this.roleText = document.querySelector('.role-text');
    if (!this.roleText) return;
    
    this.roles = ['AI Engineer', 'Machine Learning Expert', 'Deep Learning Enthusiast'];
    this.currentRole = 0;
    this.currentChar = 0;
    this.isDeleting = false;
    
    this.type();
  }

  type() {
    const current = this.roles[this.currentRole];
    const text = this.isDeleting 
      ? current.substring(0, this.currentChar--)
      : current.substring(0, this.currentChar++);
    
    this.roleText.textContent = text;
    
    let typeSpeed = this.isDeleting ? 50 : 100;
    
    if (!this.isDeleting && this.currentChar === current.length) {
      typeSpeed = 2000;
      this.isDeleting = true;
    } else if (this.isDeleting && this.currentChar === 0) {
      this.isDeleting = false;
      this.currentRole = (this.currentRole + 1) % this.roles.length;
    }
    
    setTimeout(() => this.type(), typeSpeed);
  }
}

// Main Splash Animation
class SplashAnimation {
  constructor() {
    this.images = [
      'static/images/me-pics/a2.png',
      'static/images/me-pics/a3.png',
      'static/images/me-pics/a4.png',
      'static/images/me-pics/a5.png',
      'static/images/me-pics/a6.png',
      'static/images/me-pics/a8.png',
      'static/images/me-pics/a9.png',
      'static/images/me-pics/a10.png',
      'static/images/me-pics/a11.png'
    ];
    
    this.imageContainer = document.getElementById('imageContainer');
    this.init();
  }

  init() {
    this.loadImages();
    setTimeout(() => this.animateImages(), 3000);
  }

  loadImages() {
    this.images.forEach(src => {
      const img = document.createElement('img');
      img.src = src;
      img.style.display = 'none';
      this.imageContainer.appendChild(img);
    });
  }

  selectRandomImages(count) {
    const shuffled = [...this.images].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  animateImages() {
    const selectedImages = this.selectRandomImages(2);
    const [img1, img2] = selectedImages.map(src => {
      const img = this.imageContainer.querySelector(`img[src="${src}"]`);
      img.style.display = 'block';
      return img;
    });

    const mainBannerRect = document.getElementById('mainProfilePic').getBoundingClientRect();
    const aboutRect = document.getElementById('aboutProfilePic').getBoundingClientRect();

    const translateX1 = mainBannerRect.left;
    const translateY1 = mainBannerRect.top;
    const translateX2 = aboutRect.left;
    const translateY2 = aboutRect.top;

    img1.style.transform = `translate(${translateX1}px, ${translateY1}px) scale(1.2)`;
    img2.style.transform = `translate(${translateX2}px, ${translateY2}px) scale(1.2)`;

    setTimeout(() => {
      document.getElementById('mainProfilePic').src = img1.src;
      document.getElementById('aboutProfilePic').src = img2.src;
      this.fadeOutSplash();
    }, 1500);
  }

  fadeOutSplash() {
    const splash = document.getElementById('splash');
    splash.style.transition = 'opacity 1s ease';
    splash.style.opacity = 0;
    setTimeout(() => {
      splash.style.display = 'none';
    }, 1000);
  }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ParticleSystem();
  new MorphingLogo();
  new TypingEffect();
  new SplashAnimation();
});
