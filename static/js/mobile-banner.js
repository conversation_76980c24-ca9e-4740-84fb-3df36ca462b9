/**
 * Mobile Banner Optimization
 * Ensures proper image sizing and positioning on mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
  // Get the profile image element
  const profileImage = document.getElementById('mainProfilePic');
  const profileContainer = document.querySelector('.profile-image-container');
  
  // Function to adjust image size and position based on screen size
  function adjustImageForMobile() {
    if (window.innerWidth <= 576) {
      // Mobile view
      if (profileContainer) {
        // Set a fixed height for better proportions on mobile
        const containerWidth = profileContainer.offsetWidth;
        profileContainer.style.height = `${containerWidth}px`;
      }
    } else if (window.innerWidth <= 768) {
      // Tablet view
      if (profileContainer) {
        // Adjust height for tablets
        const containerWidth = profileContainer.offsetWidth;
        profileContainer.style.height = `${containerWidth * 0.9}px`;
      }
    } else {
      // Reset for larger screens
      if (profileContainer) {
        profileContainer.style.height = '';
      }
    }
  }
  
  // Call on page load
  adjustImageForMobile();
  
  // Call on window resize
  window.addEventListener('resize', adjustImageForMobile);
  
  // Call when image is loaded
  if (profileImage) {
    profileImage.addEventListener('load', adjustImageForMobile);
  }
});
