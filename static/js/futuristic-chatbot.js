/**
 * Futuristic AI Chatbot - Uncommon, Modern UI with Wow Effects
 * A neural network-inspired chat interface with holographic elements
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Neural Network Background
  initNeuralNetworkBackground();

  // Initialize Chat Interface
  initNeuralChatInterface();

  // Initialize Floating Message
  initFloatingMessage();
});

/**
 * Initialize Neural Network Background
 * Creates an animated neural network background for the chat interface
 */
function initNeuralNetworkBackground() {
  const neuralBg = document.querySelector('.neural-network-bg');
  if (!neuralBg) return;

  // Create canvas for neural network visualization
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  neuralBg.appendChild(canvas);

  // Set canvas size
  function setCanvasSize() {
    canvas.width = neuralBg.offsetWidth;
    canvas.height = neuralBg.offsetHeight;
  }

  setCanvasSize();
  window.addEventListener('resize', setCanvasSize);

  // Neural network nodes and connections
  const nodes = [];
  const connections = [];
  const nodeCount = 30;
  const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();

  // Create nodes
  for (let i = 0; i < nodeCount; i++) {
    nodes.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 2 + 1,
      vx: Math.random() * 0.5 - 0.25,
      vy: Math.random() * 0.5 - 0.25
    });
  }

  // Create connections between nodes
  for (let i = 0; i < nodeCount; i++) {
    for (let j = i + 1; j < nodeCount; j++) {
      if (Math.random() > 0.85) {
        connections.push({
          from: i,
          to: j,
          opacity: Math.random() * 0.5 + 0.1
        });
      }
    }
  }

  // Animate neural network
  function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update and draw connections
    ctx.strokeStyle = primaryColor;
    ctx.lineWidth = 0.5;

    connections.forEach(connection => {
      const fromNode = nodes[connection.from];
      const toNode = nodes[connection.to];

      ctx.beginPath();
      ctx.moveTo(fromNode.x, fromNode.y);
      ctx.lineTo(toNode.x, toNode.y);
      ctx.globalAlpha = connection.opacity;
      ctx.stroke();
    });

    // Update and draw nodes
    ctx.globalAlpha = 1;
    ctx.fillStyle = primaryColor;

    nodes.forEach(node => {
      // Update position
      node.x += node.vx;
      node.y += node.vy;

      // Bounce off edges
      if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
      if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

      // Draw node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
      ctx.fill();
    });

    requestAnimationFrame(animate);
  }

  animate();
}

/**
 * Initialize Neural Chat Interface
 * Sets up the futuristic chat interface functionality
 */
function initNeuralChatInterface() {
  // DOM Elements
  const chatToggle = document.getElementById('chat-toggle');
  const chatInterface = document.getElementById('neural-chat-interface');
  const closeChat = document.getElementById('close-neural-chat');
  const minimizeChat = document.getElementById('minimize-neural-chat');
  const chatBody = document.getElementById('neural-chat-body');
  const inputField = document.getElementById('neural-input-field');
  const sendButton = document.getElementById('neural-send-btn');
  const suggestions = document.querySelectorAll('.neural-suggestion');

  // Toggle chat interface visibility
  if (chatToggle && chatInterface) {
    chatToggle.addEventListener('click', function() {
      chatInterface.classList.toggle('active');

      if (chatInterface.classList.contains('active')) {
        inputField.focus();
        chatBody.scrollTop = chatBody.scrollHeight;
      }
    });
  }

  // Close chat interface
  if (closeChat && chatInterface) {
    closeChat.addEventListener('click', function() {
      chatInterface.classList.remove('active');
    });
  }

  // Minimize chat interface
  if (minimizeChat && chatInterface) {
    minimizeChat.addEventListener('click', function() {
      chatInterface.classList.remove('active');
    });
  }

  // Auto-resize input field
  if (inputField) {
    inputField.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });

    // Handle Enter key
    inputField.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });
  }

  // Send button click
  if (sendButton) {
    sendButton.addEventListener('click', sendMessage);
  }

  // Suggestion clicks
  if (suggestions) {
    suggestions.forEach(suggestion => {
      suggestion.addEventListener('click', function() {
        if (inputField) {
          inputField.value = this.textContent;
          inputField.focus();
          // Auto-resize input after setting value
          inputField.dispatchEvent(new Event('input'));
        }
      });
    });
  }

  // Send message function
  async function sendMessage() {
    if (!inputField || !chatBody) return;

    const message = inputField.value.trim();
    if (message === '') return;

    // Clear input field
    inputField.value = '';
    inputField.style.height = 'auto';

    // Add user message to chat
    addMessage(message, 'user');

    // Show typing indicator
    showTypingIndicator();

    try {
      // Send message to server
      const response = await fetch('/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ question: message })
      });

      const data = await response.json();

      // Remove typing indicator
      removeTypingIndicator();

      // Add AI response
      if (data.response) {
        addMessage(data.response, 'ai');
      } else {
        addMessage("I'm sorry, I couldn't process that request.", 'ai');
      }
    } catch (error) {
      console.error('Error:', error);
      removeTypingIndicator();
      addMessage("I'm having trouble connecting. Please try again later.", 'ai');
    }

    // Scroll to bottom
    chatBody.scrollTop = chatBody.scrollHeight;
  }

  // Add message to chat
  function addMessage(content, sender) {
    if (!chatBody) return;

    const messageElement = document.createElement('div');
    messageElement.classList.add('neural-message', sender);

    // Create avatar
    const avatar = document.createElement('div');
    avatar.classList.add('neural-avatar', sender);

    const avatarImg = document.createElement('img');
    if (sender === 'user') {
      avatarImg.src = '/static/images/person.svg';
      avatarImg.alt = 'User';
    } else {
      avatarImg.src = '/static/images/ai.jpg';
      avatarImg.alt = 'AI';
    }
    avatar.appendChild(avatarImg);

    // Create message content
    const messageContent = document.createElement('div');
    messageContent.classList.add('neural-message-content');

    // Parse markdown formatting if it's an AI message
    if (sender === 'ai') {
      messageContent.innerHTML = parseMarkdown(escapeHtml(content));
    } else {
      // For user messages, just escape HTML
      messageContent.textContent = content;
    }

    // Assemble message
    messageElement.appendChild(avatar);
    messageElement.appendChild(messageContent);

    chatBody.appendChild(messageElement);
    chatBody.scrollTop = chatBody.scrollHeight;
  }

  // Parse markdown-style formatting
  function parseMarkdown(text) {
    // Bold: **text** or __text__
    text = text.replace(/\*\*(.*?)\*\*|__(.*?)__/g, '<strong>$1$2</strong>');

    // Italic: *text* or _text_
    text = text.replace(/\*(.*?)\*|_(.*?)_/g, '<em>$1$2</em>');

    // Code: `text`
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');

    // Links: [text](url)
    text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

    // Lists: - item or * item
    text = text.replace(/^[\*\-] (.*?)$/gm, '<li>$1</li>');
    text = text.replace(/<li>(.*?)<\/li>(\n<li>.*?<\/li>)+/g, '<ul>$&</ul>');

    // Headers: # Header or ## Header
    text = text.replace(/^# (.*?)$/gm, '<h3>$1</h3>');
    text = text.replace(/^## (.*?)$/gm, '<h4>$1</h4>');

    // Line breaks
    text = text.replace(/\n/g, '<br>');

    return text;
  }

  // Show typing indicator
  function showTypingIndicator() {
    if (!chatBody) return;

    const typingElement = document.createElement('div');
    typingElement.classList.add('neural-typing');
    typingElement.id = 'neural-typing-indicator';

    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('div');
      dot.classList.add('neural-typing-dot');
      typingElement.appendChild(dot);
    }

    chatBody.appendChild(typingElement);
    chatBody.scrollTop = chatBody.scrollHeight;
  }

  // Remove typing indicator
  function removeTypingIndicator() {
    const typingIndicator = document.getElementById('neural-typing-indicator');
    if (typingIndicator) {
      typingIndicator.remove();
    }
  }

  // Add initial welcome message
  setTimeout(() => {
    if (chatBody) {
      addMessage("Hello! I'm your AI assistant. How can I help you today?", 'ai');
    }
  }, 1000);
}

/**
 * Initialize Floating Message
 * Sets up the animated floating message that appears above the chat button
 * The message will reappear when the chat is closed, but only if it hasn't been opened before in the session
 */
function initFloatingMessage() {
  const floatingMessage = document.getElementById('floating-chat-message');
  const chatToggle = document.getElementById('chat-toggle');
  const chatInterface = document.getElementById('neural-chat-interface');
  const closeChat = document.getElementById('close-neural-chat');
  const minimizeChat = document.getElementById('minimize-neural-chat');

  if (!floatingMessage || !chatToggle || !chatInterface) return;

  // Variables to track state
  let chatHasBeenOpened = false;

  // Function to show the message
  function showMessage() {
    floatingMessage.style.display = '';
    setTimeout(() => {
      floatingMessage.classList.add('show');
    }, 100); // Small delay to ensure display change takes effect first
  }

  // Function to hide the message
  function hideMessage() {
    floatingMessage.classList.remove('show');
    setTimeout(() => {
      floatingMessage.style.display = 'none';
    }, 500); // Wait for transition to complete
  }

  // Function to check if chat is active
  function isChatActive() {
    return chatInterface.classList.contains('active');
  }

  // Hide message initially to prevent flashing
  hideMessage();

  // Show message initially with delay
  setTimeout(() => {
    if (!isChatActive() && !chatHasBeenOpened) {
      showMessage();
    }
  }, 3000);

  // Handle chat toggle button click
  chatToggle.addEventListener('click', () => {
    // Hide message when chat is opened
    hideMessage();

    // Mark that chat has been opened
    chatHasBeenOpened = true;
  });

  // Handle chat close/minimize
  function handleChatClose() {
    // If chat has not been opened before, show message again when chat is closed
    if (!chatHasBeenOpened) {
      // Show message again when chat is closed
      setTimeout(() => {
        showMessage();
      }, 500); // Small delay after closing
    }
  }

  // Add event listeners to close and minimize buttons
  if (closeChat) {
    closeChat.addEventListener('click', handleChatClose);
  }

  if (minimizeChat) {
    minimizeChat.addEventListener('click', handleChatClose);
  }

  // Set up message cycle (show/hide periodically)
  function cycleMessage() {
    // Only cycle if chat is not active and has not been opened
    if (!isChatActive() && !chatHasBeenOpened) {
      if (floatingMessage.classList.contains('show')) {
        hideMessage();
      } else {
        showMessage();
      }
    } else {
      // If chat is active or has been opened, ensure message is hidden
      hideMessage();
    }
  }

  // Cycle the message every 15 seconds
  const messageCycleInterval = setInterval(cycleMessage, 15000);

  // Add event listener to clean up interval when page unloads
  window.addEventListener('beforeunload', () => {
    clearInterval(messageCycleInterval);
  });
}

// Helper function to escape HTML
function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}
