/**
 * AI Projects Section - Interactive Elements
 * Modern, eye-catching UI for AI student portfolio
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Neural Network Background
  initNeuralNetworkBackground();

  // Initialize Mobile Carousel
  initProjectsCarousel();

  // Add hover effects to project cards
  initProjectCardEffects();
});

/**
 * Initialize Neural Network Background
 * Creates an animated neural network background using canvas
 */
function initNeuralNetworkBackground() {
  const canvas = document.getElementById('neuralNetworkCanvas');
  if (!canvas) return;

  const ctx = canvas.getContext('2d');
  const width = canvas.width = window.innerWidth;
  const height = canvas.height = window.innerHeight;

  // Neural network nodes
  const nodes = [];
  const nodeCount = Math.floor(width * height / 20000); // Adjust density based on screen size

  // Create nodes
  for (let i = 0; i < nodeCount; i++) {
    nodes.push({
      x: Math.random() * width,
      y: Math.random() * height,
      radius: Math.random() * 2 + 1,
      vx: Math.random() * 0.5 - 0.25,
      vy: Math.random() * 0.5 - 0.25,
      color: `rgba(${180 + Math.random() * 75}, ${44 + Math.random() * 30}, ${29 + Math.random() * 20}, ${Math.random() * 0.3 + 0.1})`
    });
  }

  // Animation function
  function animate() {
    ctx.clearRect(0, 0, width, height);

    // Update and draw nodes
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];

      // Update position
      node.x += node.vx;
      node.y += node.vy;

      // Bounce off edges
      if (node.x < 0 || node.x > width) node.vx *= -1;
      if (node.y < 0 || node.y > height) node.vy *= -1;

      // Draw node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
      ctx.fillStyle = node.color;
      ctx.fill();

      // Connect nodes that are close to each other
      for (let j = i + 1; j < nodes.length; j++) {
        const node2 = nodes[j];
        const dx = node.x - node2.x;
        const dy = node.y - node2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          ctx.beginPath();
          ctx.moveTo(node.x, node.y);
          ctx.lineTo(node2.x, node2.y);
          ctx.strokeStyle = `rgba(180, 44, 29, ${0.1 * (1 - distance / 100)})`;
          ctx.lineWidth = 0.5;
          ctx.stroke();
        }
      }
    }

    requestAnimationFrame(animate);
  }

  // Start animation
  animate();

  // Handle window resize
  window.addEventListener('resize', function() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  });
}

/**
 * Initialize Mobile Projects Carousel
 * Creates a touch-friendly carousel for mobile devices
 */
function initProjectsCarousel() {
  const carousel = document.querySelector('.carousel-container');
  if (!carousel) return;

  const slides = document.querySelectorAll('.carousel-slide');
  const dotsContainer = document.querySelector('.carousel-dots');
  const prevBtn = document.getElementById('prevCarouselBtn');
  const nextBtn = document.getElementById('nextCarouselBtn');

  let currentIndex = 0;

  // Create dots
  slides.forEach((_, index) => {
    const dot = document.createElement('div');
    dot.classList.add('carousel-dot');
    if (index === 0) dot.classList.add('active');
    dot.addEventListener('click', () => goToSlide(index));
    dotsContainer.appendChild(dot);
  });

  // Update carousel position
  function updateCarousel() {
    // Calculate offset based on dynamic slide widths

    // Calculate the total offset based on the width of each slide up to the current one
    let offset = 0;
    for (let i = 0; i < currentIndex; i++) {
      offset += slides[i].offsetWidth + 30; // Width + margin of each previous slide
    }

    carousel.style.transform = `translateX(-${offset}px)`;

    // Update active dot
    document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
      dot.classList.toggle('active', index === currentIndex);
    });

    // Update active slide
    slides.forEach((slide, index) => {
      slide.classList.toggle('active', index === currentIndex);
    });
  }

  // Go to specific slide
  function goToSlide(index) {
    currentIndex = Math.max(0, Math.min(index, slides.length - 1));
    updateCarousel();
  }

  // Next slide
  function nextSlide() {
    currentIndex = (currentIndex + 1) % slides.length;
    updateCarousel();
  }

  // Previous slide
  function prevSlide() {
    currentIndex = (currentIndex - 1 + slides.length) % slides.length;
    updateCarousel();
  }

  // Event listeners
  if (prevBtn) prevBtn.addEventListener('click', prevSlide);
  if (nextBtn) nextBtn.addEventListener('click', nextSlide);

  // Touch events for swipe
  let touchStartX = 0;
  let touchEndX = 0;

  carousel.addEventListener('touchstart', e => {
    touchStartX = e.changedTouches[0].screenX;
  });

  carousel.addEventListener('touchend', e => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    if (touchEndX < touchStartX - swipeThreshold) {
      nextSlide();
    } else if (touchEndX > touchStartX + swipeThreshold) {
      prevSlide();
    }
  }

  // Auto-advance carousel
  setInterval(nextSlide, 5000);

  // Add resize event listener to handle window size changes
  window.addEventListener('resize', () => {
    // Recalculate positions on resize
    updateCarousel();
  });
}

/**
 * Initialize Project Card Effects
 * Adds interactive hover effects to project cards
 */
function initProjectCardEffects() {
  const projectCards = document.querySelectorAll('.ai-project-card');

  projectCards.forEach(card => {
    // Create glow elements
    const topLeftGlow = document.createElement('div');
    topLeftGlow.classList.add('ai-glow', 'top-left');

    const bottomRightGlow = document.createElement('div');
    bottomRightGlow.classList.add('ai-glow', 'bottom-right');

    const neuralLines = document.createElement('div');
    neuralLines.classList.add('neural-lines');

    card.appendChild(topLeftGlow);
    card.appendChild(bottomRightGlow);
    card.appendChild(neuralLines);

    // Mouse move effect for 3D tilt
    card.addEventListener('mousemove', function(e) {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left; // x position within the element
      const y = e.clientY - rect.top; // y position within the element

      // Calculate rotation based on mouse position
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 20;
      const rotateY = (centerX - x) / 20;

      // Apply rotation transform
      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;

      // Move glow elements based on mouse position
      topLeftGlow.style.transform = `translate(${(x - centerX) / 10}px, ${(y - centerY) / 10}px)`;
      bottomRightGlow.style.transform = `translate(${(centerX - x) / 10}px, ${(centerY - y) / 10}px)`;
    });

    // Reset transform on mouse leave
    card.addEventListener('mouseleave', function() {
      card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
      topLeftGlow.style.transform = 'translate(0, 0)';
      bottomRightGlow.style.transform = 'translate(0, 0)';
    });
  });
}
