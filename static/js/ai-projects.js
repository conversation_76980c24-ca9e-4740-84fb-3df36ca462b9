/**
 * AI Projects Section - Ultra-Advanced Interactive Elements
 * Futuristic, cutting-edge UI for AI student portfolio
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Enhanced Neural Network Background
  initAdvancedNeuralNetworkBackground();

  // Initialize Advanced Mobile Carousel
  initAdvancedProjectsCarousel();

  // Add advanced hover effects to project cards
  initAdvancedProjectCardEffects();

  // Initialize stagger animations for cards
  initStaggerAnimations();

  // Initialize advanced particle system
  initAdvancedParticleSystem();

  // Initialize intersection observer for animations
  initIntersectionObserver();
});

/**
 * Initialize Neural Network Background
 * Creates an animated neural network background using canvas
 */
function initAdvancedNeuralNetworkBackground() {
  const canvas = document.getElementById('neuralNetworkCanvas');
  if (!canvas) return;

  const ctx = canvas.getContext('2d');
  const container = canvas.parentElement;

  // Set canvas size with device pixel ratio for crisp rendering
  function resizeCanvas() {
    const dpr = window.devicePixelRatio || 1;
    const rect = container.getBoundingClientRect();

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    ctx.scale(dpr, dpr);
  }

  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);

  // Optimized neural network nodes
  const nodes = [];
  const nodeCount = 30; // Reduced from 80
  const pulseNodes = [];

  // Create simplified nodes
  for (let i = 0; i < nodeCount; i++) {
    nodes.push({
      x: Math.random() * container.offsetWidth,
      y: Math.random() * container.offsetHeight,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      radius: Math.random() * 3 + 1,
      energy: Math.random()
    });
  }

  // Reduced pulse nodes
  for (let i = 0; i < 2; i++) {
    pulseNodes.push({
      x: Math.random() * container.offsetWidth,
      y: Math.random() * container.offsetHeight,
      radius: 0,
      maxRadius: 30 + Math.random() * 20,
      phase: Math.random() * Math.PI * 2,
      speed: 0.01 + Math.random() * 0.01
    });
  }

  let time = 0;

  // Optimized animation function
  function animate() {
    time += 0.016;

    // Clear canvas efficiently
    ctx.clearRect(0, 0, container.offsetWidth, container.offsetHeight);

    // Update and draw pulse nodes (simplified)
    pulseNodes.forEach(pulse => {
      pulse.phase += pulse.speed;
      pulse.radius = (Math.sin(pulse.phase) * 0.5 + 0.5) * pulse.maxRadius;

      ctx.beginPath();
      ctx.arc(pulse.x, pulse.y, pulse.radius, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(180, 44, 29, ${0.05 * (1 - pulse.radius / pulse.maxRadius)})`;
      ctx.lineWidth = 1;
      ctx.stroke();
    });

    // Update and draw main nodes (optimized)
    nodes.forEach((node, i) => {
      // Update position with boundary wrapping
      node.x += node.vx;
      node.y += node.vy;

      if (node.x < -10) node.x = container.offsetWidth + 10;
      if (node.x > container.offsetWidth + 10) node.x = -10;
      if (node.y < -10) node.y = container.offsetHeight + 10;
      if (node.y > container.offsetHeight + 10) node.y = -10;

      // Simplified energy calculation
      node.energy = Math.sin(time + i * 0.2) * 0.3 + 0.7;

      // Draw simple node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(180, 44, 29, ${node.energy * 0.3})`;
      ctx.fill();

      // Draw connections (reduced frequency)
      if (i % 2 === 0) { // Only check every other node for connections
        nodes.forEach((otherNode, j) => {
          if (i !== j && j > i) { // Avoid duplicate connections
            const dx = node.x - otherNode.x;
            const dy = node.y - otherNode.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 80) { // Reduced connection distance
              const connectionAlpha = (80 - distance) / 80 * 0.15;

              ctx.beginPath();
              ctx.moveTo(node.x, node.y);
              ctx.lineTo(otherNode.x, otherNode.y);
              ctx.strokeStyle = `rgba(180, 44, 29, ${connectionAlpha})`;
              ctx.lineWidth = 0.5;
              ctx.stroke();
            }
          }
        });
      }
    });

    requestAnimationFrame(animate);
  }

  // Start animation
  animate();

  // Handle window resize
  window.addEventListener('resize', function() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  });
}

/**
 * Initialize Mobile Projects Carousel
 * Creates a touch-friendly carousel for mobile devices
 */
function initAdvancedProjectsCarousel() {
  const carousel = document.querySelector('.carousel-container');
  if (!carousel) return;

  const slides = document.querySelectorAll('.carousel-slide');
  const dotsContainer = document.querySelector('.carousel-dots');
  const prevBtn = document.getElementById('prevCarouselBtn');
  const nextBtn = document.getElementById('nextCarouselBtn');

  let currentIndex = 0;
  let isAnimating = false;

  // Create enhanced dots with glow effects
  slides.forEach((_, index) => {
    const dot = document.createElement('div');
    dot.classList.add('carousel-dot');
    if (index === 0) dot.classList.add('active');

    // Add click event with animation
    dot.addEventListener('click', () => {
      if (!isAnimating) goToSlide(index);
    });

    dotsContainer.appendChild(dot);
  });

  // Update carousel position
  function updateCarousel() {
    // Calculate offset based on dynamic slide widths

    // Calculate the total offset based on the width of each slide up to the current one
    let offset = 0;
    for (let i = 0; i < currentIndex; i++) {
      offset += slides[i].offsetWidth + 30; // Width + margin of each previous slide
    }

    carousel.style.transform = `translateX(-${offset}px)`;

    // Update active dot
    document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
      dot.classList.toggle('active', index === currentIndex);
    });

    // Update active slide
    slides.forEach((slide, index) => {
      slide.classList.toggle('active', index === currentIndex);
    });
  }

  // Go to specific slide
  function goToSlide(index) {
    currentIndex = Math.max(0, Math.min(index, slides.length - 1));
    updateCarousel();
  }

  // Next slide
  function nextSlide() {
    currentIndex = (currentIndex + 1) % slides.length;
    updateCarousel();
  }

  // Previous slide
  function prevSlide() {
    currentIndex = (currentIndex - 1 + slides.length) % slides.length;
    updateCarousel();
  }

  // Event listeners
  if (prevBtn) prevBtn.addEventListener('click', prevSlide);
  if (nextBtn) nextBtn.addEventListener('click', nextSlide);

  // Touch events for swipe
  let touchStartX = 0;
  let touchEndX = 0;

  carousel.addEventListener('touchstart', e => {
    touchStartX = e.changedTouches[0].screenX;
  });

  carousel.addEventListener('touchend', e => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    if (touchEndX < touchStartX - swipeThreshold) {
      nextSlide();
    } else if (touchEndX > touchStartX + swipeThreshold) {
      prevSlide();
    }
  }

  // Auto-advance carousel
  setInterval(nextSlide, 5000);

  // Add resize event listener to handle window size changes
  window.addEventListener('resize', () => {
    // Recalculate positions on resize
    updateCarousel();
  });
}

/**
 * Initialize Project Card Effects
 * Adds interactive hover effects to project cards
 */
function initAdvancedProjectCardEffects() {
  const projectCards = document.querySelectorAll('.ai-project-card');

  projectCards.forEach((card) => {
    // Create simplified glow elements
    const topLeftGlow = document.createElement('div');
    topLeftGlow.classList.add('ai-glow', 'top-left');

    const bottomRightGlow = document.createElement('div');
    bottomRightGlow.classList.add('ai-glow', 'bottom-right');

    const neuralLines = document.createElement('div');
    neuralLines.classList.add('neural-lines');

    card.appendChild(topLeftGlow);
    card.appendChild(bottomRightGlow);
    card.appendChild(neuralLines);

    // Simplified mouse move effect
    card.addEventListener('mouseenter', function() {
      card.style.transform = 'translateY(-10px) scale(1.02)';
    });

    // Reset on mouse leave
    card.addEventListener('mouseleave', function() {
      card.style.transform = 'translateY(0) scale(1)';
    });
  });
}

/**
 * Initialize Stagger Animations (Simplified)
 * Creates sequential animations for project cards
 */
function initStaggerAnimations() {
  const projectCards = document.querySelectorAll('.ai-project-card');

  projectCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = 'all 0.6s ease-out';

    setTimeout(() => {
      card.style.opacity = '1';
      card.style.transform = 'translateY(0)';
    }, index * 100);
  });
}

/**
 * Initialize Simplified Particle System
 * Creates minimal floating particles
 */
function initAdvancedParticleSystem() {
  // Disabled for performance - using CSS animations instead
  return;
}

/**
 * Initialize Intersection Observer
 * Triggers animations when elements come into view
 */
function initIntersectionObserver() {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');

        // Trigger stagger animation for project cards
        if (entry.target.classList.contains('projects-grid')) {
          initStaggerAnimations();
        }
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '50px'
  });

  // Observe project section elements
  const elementsToObserve = document.querySelectorAll('.ai-projects .section-header, .projects-grid, .view-all-projects');
  elementsToObserve.forEach(el => observer.observe(el));
}
