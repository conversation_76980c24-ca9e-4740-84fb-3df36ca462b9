/**
 * AI Projects Section - Ultra-Advanced Interactive Elements
 * Futuristic, cutting-edge UI for AI student portfolio
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Enhanced Neural Network Background
  initAdvancedNeuralNetworkBackground();

  // Initialize Advanced Mobile Carousel
  initAdvancedProjectsCarousel();

  // Add advanced hover effects to project cards
  initAdvancedProjectCardEffects();

  // Initialize stagger animations for cards
  initStaggerAnimations();

  // Initialize advanced particle system
  initAdvancedParticleSystem();

  // Initialize intersection observer for animations
  initIntersectionObserver();
});

/**
 * Initialize Neural Network Background
 * Creates an animated neural network background using canvas
 */
function initAdvancedNeuralNetworkBackground() {
  const canvas = document.getElementById('neuralNetworkCanvas');
  if (!canvas) return;

  const ctx = canvas.getContext('2d');
  const container = canvas.parentElement;

  // Set canvas size with device pixel ratio for crisp rendering
  function resizeCanvas() {
    const dpr = window.devicePixelRatio || 1;
    const rect = container.getBoundingClientRect();

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    ctx.scale(dpr, dpr);
  }

  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);

  // Enhanced neural network nodes with different types
  const nodes = [];
  const nodeCount = 80;
  const pulseNodes = [];

  // Create diverse nodes
  for (let i = 0; i < nodeCount; i++) {
    const nodeType = Math.random();
    nodes.push({
      x: Math.random() * container.offsetWidth,
      y: Math.random() * container.offsetHeight,
      vx: (Math.random() - 0.5) * 0.8,
      vy: (Math.random() - 0.5) * 0.8,
      radius: Math.random() * 4 + 2,
      type: nodeType < 0.7 ? 'normal' : nodeType < 0.9 ? 'pulse' : 'data',
      pulsePhase: Math.random() * Math.PI * 2,
      energy: Math.random()
    });
  }

  // Create pulse nodes for special effects
  for (let i = 0; i < 5; i++) {
    pulseNodes.push({
      x: Math.random() * container.offsetWidth,
      y: Math.random() * container.offsetHeight,
      radius: 0,
      maxRadius: 50 + Math.random() * 30,
      phase: Math.random() * Math.PI * 2,
      speed: 0.02 + Math.random() * 0.02
    });
  }

  let time = 0;

  // Advanced animation function with enhanced effects
  function animate() {
    time += 0.016; // ~60fps

    // Create dynamic background gradient
    const gradient = ctx.createRadialGradient(
      container.offsetWidth * 0.5, container.offsetHeight * 0.5, 0,
      container.offsetWidth * 0.5, container.offsetHeight * 0.5, container.offsetWidth * 0.8
    );
    gradient.addColorStop(0, 'rgba(180, 44, 29, 0.02)');
    gradient.addColorStop(0.5, 'rgba(255, 126, 95, 0.01)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, container.offsetWidth, container.offsetHeight);

    // Update and draw pulse nodes
    pulseNodes.forEach(pulse => {
      pulse.phase += pulse.speed;
      pulse.radius = (Math.sin(pulse.phase) * 0.5 + 0.5) * pulse.maxRadius;

      ctx.beginPath();
      ctx.arc(pulse.x, pulse.y, pulse.radius, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(180, 44, 29, ${0.1 * (1 - pulse.radius / pulse.maxRadius)})`;
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Update and draw main nodes
    nodes.forEach((node, i) => {
      // Update position with boundary wrapping
      node.x += node.vx;
      node.y += node.vy;

      if (node.x < -10) node.x = container.offsetWidth + 10;
      if (node.x > container.offsetWidth + 10) node.x = -10;
      if (node.y < -10) node.y = container.offsetHeight + 10;
      if (node.y > container.offsetHeight + 10) node.y = -10;

      // Update energy and pulse phase
      node.energy = Math.sin(time * 2 + i * 0.1) * 0.5 + 0.5;
      node.pulsePhase += 0.05;

      // Draw node based on type
      const alpha = 0.4 + node.energy * 0.4;

      if (node.type === 'pulse') {
        const pulseSize = node.radius + Math.sin(node.pulsePhase) * 2;
        ctx.beginPath();
        ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 126, 95, ${alpha})`;
        ctx.fill();

        // Outer glow
        ctx.beginPath();
        ctx.arc(node.x, node.y, pulseSize + 3, 0, Math.PI * 2);
        ctx.strokeStyle = `rgba(180, 44, 29, ${alpha * 0.3})`;
        ctx.lineWidth = 1;
        ctx.stroke();
      } else if (node.type === 'data') {
        ctx.fillStyle = `rgba(180, 44, 29, ${alpha})`;
        ctx.fillRect(node.x - node.radius/2, node.y - node.radius/2, node.radius, node.radius);
      } else {
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(180, 44, 29, ${alpha})`;
        ctx.fill();
      }

      // Draw enhanced connections with data flow
      nodes.forEach((otherNode, j) => {
        if (i !== j) {
          const dx = node.x - otherNode.x;
          const dy = node.y - otherNode.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 120) {
            const connectionAlpha = (120 - distance) / 120 * 0.3;

            // Main connection line
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            ctx.lineTo(otherNode.x, otherNode.y);
            ctx.strokeStyle = `rgba(180, 44, 29, ${connectionAlpha})`;
            ctx.lineWidth = 1;
            ctx.stroke();

            // Data flow effect
            if (Math.random() < 0.01) {
              const flowX = node.x + (otherNode.x - node.x) * (Math.sin(time * 3) * 0.5 + 0.5);
              const flowY = node.y + (otherNode.y - node.y) * (Math.sin(time * 3) * 0.5 + 0.5);

              ctx.beginPath();
              ctx.arc(flowX, flowY, 2, 0, Math.PI * 2);
              ctx.fillStyle = `rgba(255, 126, 95, ${connectionAlpha * 2})`;
              ctx.fill();
            }
          }
        }
      });
    });

    requestAnimationFrame(animate);
  }

  // Start animation
  animate();

  // Handle window resize
  window.addEventListener('resize', function() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  });
}

/**
 * Initialize Mobile Projects Carousel
 * Creates a touch-friendly carousel for mobile devices
 */
function initAdvancedProjectsCarousel() {
  const carousel = document.querySelector('.carousel-container');
  if (!carousel) return;

  const slides = document.querySelectorAll('.carousel-slide');
  const dotsContainer = document.querySelector('.carousel-dots');
  const prevBtn = document.getElementById('prevCarouselBtn');
  const nextBtn = document.getElementById('nextCarouselBtn');

  let currentIndex = 0;
  let isAnimating = false;

  // Create enhanced dots with glow effects
  slides.forEach((_, index) => {
    const dot = document.createElement('div');
    dot.classList.add('carousel-dot');
    if (index === 0) dot.classList.add('active');

    // Add click event with animation
    dot.addEventListener('click', () => {
      if (!isAnimating) goToSlide(index);
    });

    dotsContainer.appendChild(dot);
  });

  // Update carousel position
  function updateCarousel() {
    // Calculate offset based on dynamic slide widths

    // Calculate the total offset based on the width of each slide up to the current one
    let offset = 0;
    for (let i = 0; i < currentIndex; i++) {
      offset += slides[i].offsetWidth + 30; // Width + margin of each previous slide
    }

    carousel.style.transform = `translateX(-${offset}px)`;

    // Update active dot
    document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
      dot.classList.toggle('active', index === currentIndex);
    });

    // Update active slide
    slides.forEach((slide, index) => {
      slide.classList.toggle('active', index === currentIndex);
    });
  }

  // Go to specific slide
  function goToSlide(index) {
    currentIndex = Math.max(0, Math.min(index, slides.length - 1));
    updateCarousel();
  }

  // Next slide
  function nextSlide() {
    currentIndex = (currentIndex + 1) % slides.length;
    updateCarousel();
  }

  // Previous slide
  function prevSlide() {
    currentIndex = (currentIndex - 1 + slides.length) % slides.length;
    updateCarousel();
  }

  // Event listeners
  if (prevBtn) prevBtn.addEventListener('click', prevSlide);
  if (nextBtn) nextBtn.addEventListener('click', nextSlide);

  // Touch events for swipe
  let touchStartX = 0;
  let touchEndX = 0;

  carousel.addEventListener('touchstart', e => {
    touchStartX = e.changedTouches[0].screenX;
  });

  carousel.addEventListener('touchend', e => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    if (touchEndX < touchStartX - swipeThreshold) {
      nextSlide();
    } else if (touchEndX > touchStartX + swipeThreshold) {
      prevSlide();
    }
  }

  // Auto-advance carousel
  setInterval(nextSlide, 5000);

  // Add resize event listener to handle window size changes
  window.addEventListener('resize', () => {
    // Recalculate positions on resize
    updateCarousel();
  });
}

/**
 * Initialize Project Card Effects
 * Adds interactive hover effects to project cards
 */
function initAdvancedProjectCardEffects() {
  const projectCards = document.querySelectorAll('.ai-project-card');

  projectCards.forEach((card, index) => {
    // Create enhanced glow elements
    const topLeftGlow = document.createElement('div');
    topLeftGlow.classList.add('ai-glow', 'top-left');

    const bottomRightGlow = document.createElement('div');
    bottomRightGlow.classList.add('ai-glow', 'bottom-right');

    const neuralLines = document.createElement('div');
    neuralLines.classList.add('neural-lines');

    card.appendChild(topLeftGlow);
    card.appendChild(bottomRightGlow);
    card.appendChild(neuralLines);

    // Enhanced mouse move effect for advanced 3D tilt
    card.addEventListener('mousemove', function(e) {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Calculate enhanced rotation with perspective
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 15;
      const rotateY = (centerX - x) / 15;

      // Apply advanced 3D transform with scale
      card.style.transform = `
        perspective(1200px)
        rotateX(${rotateX}deg)
        rotateY(${rotateY}deg)
        translateZ(20px)
        scale(1.02)
      `;

      // Enhanced glow movement with parallax effect
      const parallaxX = (x - centerX) / 8;
      const parallaxY = (y - centerY) / 8;

      topLeftGlow.style.transform = `translate(${parallaxX}px, ${parallaxY}px) scale(1.1)`;
      bottomRightGlow.style.transform = `translate(${-parallaxX}px, ${-parallaxY}px) scale(1.1)`;

      // Dynamic neural lines opacity based on mouse position
      const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
      const maxDistance = Math.sqrt(Math.pow(centerX, 2) + Math.pow(centerY, 2));
      const opacity = 1 - (distance / maxDistance);
      neuralLines.style.opacity = opacity * 0.6;
    });

    // Enhanced reset with smooth transition
    card.addEventListener('mouseleave', function() {
      card.style.transform = 'perspective(1200px) rotateX(0) rotateY(0) translateZ(0) scale(1)';
      topLeftGlow.style.transform = 'translate(0, 0) scale(1)';
      bottomRightGlow.style.transform = 'translate(0, 0) scale(1)';
      neuralLines.style.opacity = '0';
    });

    // Add click ripple effect
    card.addEventListener('click', function(e) {
      const ripple = document.createElement('div');
      ripple.style.position = 'absolute';
      ripple.style.borderRadius = '50%';
      ripple.style.background = 'rgba(180, 44, 29, 0.3)';
      ripple.style.transform = 'scale(0)';
      ripple.style.animation = 'ripple 0.6s linear';
      ripple.style.pointerEvents = 'none';

      const rect = card.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
      ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

      card.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
}

/**
 * Initialize Stagger Animations
 * Creates sequential animations for project cards
 */
function initStaggerAnimations() {
  const projectCards = document.querySelectorAll('.ai-project-card');

  projectCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(50px) scale(0.9)';
    card.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.320, 1)';

    setTimeout(() => {
      card.style.opacity = '1';
      card.style.transform = 'translateY(0) scale(1)';
    }, index * 150);
  });
}

/**
 * Initialize Advanced Particle System
 * Creates floating particles around project cards
 */
function initAdvancedParticleSystem() {
  const projectsSection = document.querySelector('.ai-projects');
  if (!projectsSection) return;

  const particleCount = 30;
  const particles = [];

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.style.position = 'absolute';
    particle.style.width = Math.random() * 4 + 2 + 'px';
    particle.style.height = particle.style.width;
    particle.style.background = `rgba(180, 44, 29, ${Math.random() * 0.3 + 0.1})`;
    particle.style.borderRadius = '50%';
    particle.style.pointerEvents = 'none';
    particle.style.zIndex = '0';

    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;

    particle.style.left = x + 'px';
    particle.style.top = y + 'px';

    particles.push({
      element: particle,
      x: x,
      y: y,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      life: Math.random() * 100
    });

    projectsSection.appendChild(particle);
  }

  function animateParticles() {
    particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life += 0.5;

      // Wrap around screen
      if (particle.x < 0) particle.x = window.innerWidth;
      if (particle.x > window.innerWidth) particle.x = 0;
      if (particle.y < 0) particle.y = window.innerHeight;
      if (particle.y > window.innerHeight) particle.y = 0;

      // Update position
      particle.element.style.left = particle.x + 'px';
      particle.element.style.top = particle.y + 'px';

      // Pulse effect
      const scale = 1 + Math.sin(particle.life * 0.1) * 0.3;
      particle.element.style.transform = `scale(${scale})`;
    });

    requestAnimationFrame(animateParticles);
  }

  animateParticles();
}

/**
 * Initialize Intersection Observer
 * Triggers animations when elements come into view
 */
function initIntersectionObserver() {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');

        // Trigger stagger animation for project cards
        if (entry.target.classList.contains('projects-grid')) {
          initStaggerAnimations();
        }
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '50px'
  });

  // Observe project section elements
  const elementsToObserve = document.querySelectorAll('.ai-projects .section-header, .projects-grid, .view-all-projects');
  elementsToObserve.forEach(el => observer.observe(el));
}
