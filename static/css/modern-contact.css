/* Modern Contact Section Styles */
.contact {
  position: relative;
  background: #000000;
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

/* Neural Network Background for Contact Section */
.contact-neural-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

/* Section Title Styling */
.contact .section-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #b0b0b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  margin-bottom: 10px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
}

.contact .section-subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Modern Contact Container */
.contact-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

/* Contact Info Card */
.contact-info {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  background: rgba(20, 20, 20, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(180, 44, 29, 0.3);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  position: relative;
  overflow: hidden;
}

.contact-info::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #b42c1d, transparent);
}

.contact-info:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.contact-info h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: #ffffff;
  position: relative;
  padding-bottom: 15px;
}

.contact-info h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #b42c1d;
  border-radius: 2px;
}

/* Contact Items */
.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  padding: 15px;
  border-radius: 12px;
  background: rgba(30, 30, 30, 0.6);
  border: 1px solid rgba(180, 44, 29, 0.2);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(180, 44, 29, 0.15);
  transform: translateX(10px);
}

.contact-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  transition: transform 0.3s ease;
}

.contact-item:hover .contact-icon {
  transform: scale(1.2);
}

.contact-item a {
  color: #b0b0b0;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.contact-item a:hover {
  color: #b42c1d;
}

.contact-item p {
  color: #b0b0b0;
  margin: 0;
}

/* Social Media Links */
.contact-social {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(30, 30, 30, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(180, 44, 29, 0.3);
}

.social-link:hover {
  background: #b42c1d;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

/* Final Note */
.final-note {
  text-align: center;
  margin-top: 50px;
  font-size: 1.2rem;
  color: #b0b0b0;
  position: relative;
  z-index: 1;
  padding: 20px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  background: rgba(20, 20, 20, 0.5);
  backdrop-filter: blur(5px);
  border-radius: 15px;
  border: 1px solid rgba(180, 44, 29, 0.2);
}

.final-note::before {
  content: "";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: #b42c1d;
  border-radius: 2px;
}

/* Decorative Elements */
.contact-decorative-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.1), transparent 70%);
  z-index: 0;
  pointer-events: none;
}

.contact-circle-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  left: -100px;
  animation: float-circle 15s ease-in-out infinite;
}

.contact-circle-2 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  right: -50px;
  animation: float-circle 12s ease-in-out infinite reverse;
}

@keyframes float-circle {
  0% { transform: translate(0, 0); }
  50% { transform: translate(30px, -30px); }
  100% { transform: translate(0, 0); }
}

/* AI/ML Specific Elements */
.contact-binary {
  position: absolute;
  font-family: monospace;
  font-size: 12px;
  color: rgba(180, 44, 29, 0.2);
  white-space: nowrap;
  z-index: 0;
  pointer-events: none;
}

.contact-binary-1 {
  top: 20%;
  left: 5%;
  transform: rotate(-15deg);
}

.contact-binary-2 {
  bottom: 15%;
  right: 5%;
  transform: rotate(15deg);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .contact .section-title {
    font-size: 3rem;
  }
  
  .contact-info {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .contact .section-title {
    font-size: 2.5rem;
  }
  
  .contact .section-subtitle {
    font-size: 1.1rem;
  }
  
  .contact-info {
    padding: 30px;
  }
  
  .contact-info h4 {
    font-size: 1.6rem;
  }
  
  .contact-item {
    padding: 12px;
  }
  
  .social-link {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
  
  .final-note {
    font-size: 1.1rem;
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .contact .section-title {
    font-size: 2rem;
  }
  
  .contact .section-subtitle {
    font-size: 1rem;
    margin-bottom: 30px;
  }
  
  .contact-info {
    padding: 25px;
  }
  
  .contact-info h4 {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }
  
  .contact-item {
    padding: 10px;
    gap: 15px;
  }
  
  .contact-social {
    justify-content: center;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .final-note {
    font-size: 1rem;
    margin-top: 30px;
  }
}
