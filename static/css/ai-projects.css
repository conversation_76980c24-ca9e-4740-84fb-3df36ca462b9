/* AI Projects Section - Ultra-Advanced, Futuristic UI for AI Student Portfolio */

/* Main Container with Enhanced 3D Environment */
.ai-projects {
  position: relative;
  background: #000000;
  padding: 120px 0;
  color: #ffffff;
  overflow: hidden;
  perspective: 1200px;
  transform-style: preserve-3d;
}

/* Optimized Neural Network Background */
.neural-network-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.08;
  pointer-events: none;
  z-index: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(180, 44, 29, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 80% 20%, rgba(255, 126, 95, 0.05) 0%, transparent 40%);
  animation: neuralPulse 12s ease-in-out infinite;
}

@keyframes neuralPulse {
  0%, 100% {
    opacity: 0.08;
  }
  50% {
    opacity: 0.12;
  }
}

/* Simplified Floating Particles */
.ai-projects::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(180, 44, 29, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(180, 44, 29, 0.2), transparent);
  background-repeat: repeat;
  background-size: 300px 200px;
  animation: floatingParticles 30s linear infinite;
  z-index: 0;
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* Enhanced Section Header with Holographic Effects */
.ai-projects .section-header {
  position: relative;
  margin-bottom: 100px;
  z-index: 1;
  transform: translateZ(60px);
  text-align: center;
}

.ai-projects .section-title {
  position: relative;
  display: inline-block;
  font-size: 3.8rem;
  font-weight: 900;
  margin-bottom: 25px;
  background: linear-gradient(135deg, #ffffff 0%, #b42c1d 30%, #ff7e5f 70%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 3px;
  animation: titleGlow 4s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 30px rgba(180, 44, 29, 0.4));
}

@keyframes titleGlow {
  0%, 100% {
    filter: brightness(1) drop-shadow(0 0 15px rgba(180, 44, 29, 0.2));
  }
  50% {
    filter: brightness(1.1) drop-shadow(0 0 25px rgba(180, 44, 29, 0.4));
  }
}

.ai-projects .section-title::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #b42c1d, #ff7e5f, #b42c1d, transparent);
  border-radius: 2px;
  animation: accentPulse 4s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(180, 44, 29, 0.4);
}

@keyframes accentPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.ai-projects .section-subtitle {
  max-width: 800px;
  margin: 30px auto 0;
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.8;
  position: relative;
  font-weight: 300;
}

/* Revolutionary Hexagonal Grid Layout */
.projects-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  position: relative;
  z-index: 1;
  padding: 20px;
}

/* Innovative Hexagonal Project Cards */
.ai-project-card {
  position: relative;
  width: 320px;
  height: 370px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  overflow: hidden;
  border: 2px solid transparent;
  background-clip: padding-box;
}

/* Animated Border Effect */
.ai-project-card::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #b42c1d, #ff7e5f, #b42c1d, #ff7e5f);
  background-size: 400% 400%;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  z-index: -1;
  opacity: 0;
  animation: gradientShift 3s ease infinite;
  transition: opacity 0.3s ease;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.ai-project-card:hover::before {
  opacity: 1;
}

.ai-project-card:hover {
  transform: translateY(-10px) scale(1.05);
  filter: drop-shadow(0 15px 25px rgba(180, 44, 29, 0.3));
}

/* Inner Content Container */
.ai-project-card-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  right: 15%;
  bottom: 15%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

/* Circular Project Image for Hexagonal Cards */
.project-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(180, 44, 29, 0.5);
  background: linear-gradient(45deg, rgba(180, 44, 29, 0.2), rgba(255, 126, 95, 0.2));
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  filter: brightness(0.9);
}

.ai-project-card:hover .project-image {
  transform: scale(1.1);
  filter: brightness(1.1);
}

/* Project Icon Overlay */
.project-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: rgba(180, 44, 29, 0.8);
  z-index: 3;
  transition: all 0.3s ease;
}

.ai-project-card:hover .project-icon {
  color: #ff7e5f;
  transform: translate(-50%, -50%) scale(1.2);
}

/* Hexagonal Project Type Badge */
.project-type {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.9), rgba(255, 126, 95, 0.8));
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  z-index: 3;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* Central Content Area */
.project-content {
  text-align: center;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Hexagonal Card Title */
.project-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 10px 0;
  color: #ffffff;
  line-height: 1.2;
  text-align: center;
  background: linear-gradient(135deg, #ffffff, #b42c1d);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.ai-project-card:hover .project-title {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #ff7e5f, #b42c1d);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Compact Description */
.project-description {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 10px 0;
  line-height: 1.4;
  text-align: center;
  font-weight: 300;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.ai-project-card:hover .project-description {
  color: rgba(255, 255, 255, 0.9);
}

/* Compact Tech Stack for Hexagonal Cards */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
  margin: 10px 0;
}

.tech-tag {
  background: rgba(180, 44, 29, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(180, 44, 29, 0.4);
}

.ai-project-card:hover .tech-tag {
  background: rgba(180, 44, 29, 0.4);
  color: rgba(255, 255, 255, 1);
  border-color: rgba(180, 44, 29, 0.6);
}

/* Centered Project Links for Hexagonal Cards */
.project-links {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.github-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.4), rgba(255, 126, 95, 0.3));
  border: 1px solid rgba(180, 44, 29, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.github-link:hover {
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.7), rgba(255, 126, 95, 0.5));
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(180, 44, 29, 0.3);
  border-color: rgba(180, 44, 29, 0.8);
}

.github-link i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.github-link:hover i {
  transform: scale(1.1);
}

/* Advanced AI Animation Effects with Neural Pathways */
.ai-glow {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.6) 0%, rgba(255, 126, 95, 0.3) 30%, transparent 70%);
  filter: blur(25px);
  opacity: 0;
  transition: all 0.8s ease;
  pointer-events: none;
  animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.ai-project-card:hover .ai-glow {
  opacity: 1;
}

.ai-glow.top-left {
  top: -75px;
  left: -75px;
}

.ai-glow.bottom-right {
  bottom: -75px;
  right: -75px;
}

/* Simplified Neural Network Lines */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  background-image:
    linear-gradient(90deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px),
    linear-gradient(0deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.ai-project-card:hover .neural-lines {
  opacity: 0.3;
}

/* Enhanced Featured Project with Premium Styling */
.featured-project {
  grid-column: span 2;
  height: auto;
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.98), rgba(35, 35, 35, 0.95));
  border: 2px solid rgba(180, 44, 29, 0.6);
  position: relative;
}

.featured-project::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #b42c1d, #ff7e5f, #b42c1d);
  border-radius: 22px;
  z-index: -1;
  animation: featuredBorder 3s linear infinite;
}

@keyframes featuredBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.featured-project .project-image-container {
  height: 320px;
}

.featured-project .project-content {
  padding: 40px;
}

.featured-project .project-title {
  font-size: 2.2rem;
  background: linear-gradient(135deg, #ffffff, #b42c1d, #ff7e5f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.featured-project .project-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Enhanced Featured Badge with Premium Effects */
.featured-badge {
  position: absolute;
  top: 25px;
  left: 25px;
  background: linear-gradient(135deg, #b42c1d, #ff7e5f, #b42c1d);
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 800;
  z-index: 3;
  box-shadow:
    0 8px 20px rgba(180, 44, 29, 0.5),
    0 0 30px rgba(180, 44, 29, 0.4);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: featuredPulse 2s ease-in-out infinite;
}

@keyframes featuredPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 8px 20px rgba(180, 44, 29, 0.5),
      0 0 30px rgba(180, 44, 29, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 12px 25px rgba(180, 44, 29, 0.7),
      0 0 40px rgba(180, 44, 29, 0.6);
  }
}

.featured-badge i {
  font-size: 1rem;
  animation: starSpin 3s linear infinite;
}

@keyframes starSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Advanced Mobile Projects Carousel */
.mobile-projects-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 30px 0;
  display: none;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.05), transparent);
}

.carousel-container {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  perspective: 1000px;
}

.carousel-slide {
  flex: 0 0 85%;
  max-width: 380px;
  margin: 0 20px;
  transform-style: preserve-3d;
}

/* Enhanced Carousel Controls with 3D Effects */
.carousel-controls {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  gap: 30px;
}

.carousel-btn {
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.9), rgba(15, 15, 15, 0.8));
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.4);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.3), rgba(255, 126, 95, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carousel-btn:hover::before {
  opacity: 1;
}

.carousel-btn:hover {
  background: linear-gradient(145deg, rgba(180, 44, 29, 0.8), rgba(255, 126, 95, 0.6));
  transform: translateY(-5px) scale(1.1);
  box-shadow:
    0 15px 30px rgba(180, 44, 29, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(180, 44, 29, 0.8);
}

.carousel-btn i {
  font-size: 1.2rem;
  z-index: 1;
  transition: transform 0.3s ease;
}

.carousel-btn:hover i {
  transform: scale(1.2);
}

/* Enhanced Carousel Dots with Glow Effects */
.carousel-dots {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  gap: 12px;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.4s ease;
  border: 1px solid rgba(180, 44, 29, 0.3);
  position: relative;
}

.carousel-dot::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.5), rgba(255, 126, 95, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.carousel-dot.active {
  background: linear-gradient(135deg, #b42c1d, #ff7e5f);
  transform: scale(1.4);
  box-shadow: 0 0 15px rgba(180, 44, 29, 0.6);
}

.carousel-dot.active::before {
  opacity: 1;
}

/* Ultra-Advanced View All Projects Button */
.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 60px;
}

.view-all-btn {
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.9), rgba(15, 15, 15, 0.8));
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.5);
  border-radius: 40px;
  padding: 16px 40px;
  font-size: 1.1rem;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 2px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.view-all-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.6s ease;
  z-index: 2;
}

.view-all-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(135deg, #b42c1d, #ff7e5f);
  z-index: -1;
  transition: width 0.5s ease;
}

.view-all-btn:hover::before {
  left: 100%;
}

.view-all-btn:hover::after {
  width: 100%;
}

.view-all-btn:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow:
    0 20px 40px rgba(180, 44, 29, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: transparent;
}

.view-all-btn i {
  font-size: 1.3rem;
  transition: transform 0.3s ease;
  z-index: 3;
}

.view-all-btn:hover i {
  transform: rotate(360deg) scale(1.2);
}

/* Responsive Design for Hexagonal Layout */
@media (max-width: 1400px) {
  .projects-grid {
    gap: 25px;
  }

  .ai-project-card {
    width: 300px;
    height: 350px;
  }

  .ai-projects .section-title {
    font-size: 3.4rem;
  }
}

@media (max-width: 1200px) {
  .projects-grid {
    gap: 20px;
  }

  .ai-project-card {
    width: 280px;
    height: 330px;
  }

  .ai-projects .section-title {
    font-size: 3rem;
  }

  .project-image-container {
    width: 70px;
    height: 70px;
  }

  .project-icon {
    font-size: 1.8rem;
  }
}

@media (max-width: 992px) {
  .ai-projects {
    padding: 100px 0;
  }

  .ai-projects .section-title {
    font-size: 2.6rem;
  }

  .projects-grid {
    gap: 15px;
  }

  .ai-project-card {
    width: 260px;
    height: 310px;
  }

  .project-image-container {
    width: 60px;
    height: 60px;
  }

  .project-icon {
    font-size: 1.6rem;
  }

  .project-title {
    font-size: 1.1rem;
  }

  .project-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  /* Switch to Card Layout for Mobile */
  .projects-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .ai-project-card {
    width: 90%;
    max-width: 400px;
    height: auto;
    clip-path: none;
    border-radius: 20px;
    background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 2px solid rgba(180, 44, 29, 0.4);
  }

  .ai-project-card::before {
    clip-path: none;
    border-radius: 20px;
  }

  .ai-project-card-inner {
    position: static;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    padding: 20px;
  }

  .project-image-container {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
  }

  .project-icon {
    font-size: 2rem;
  }

  .project-content {
    height: auto;
  }

  .ai-projects {
    padding: 80px 0;
  }

  .ai-projects .section-title {
    font-size: 2.2rem;
  }

  .mobile-projects-carousel {
    display: none;
  }
}

@media (max-width: 576px) {
  .ai-projects {
    padding: 60px 0;
  }

  .ai-projects .section-title {
    font-size: 1.9rem;
  }

  .ai-project-card {
    width: 95%;
  }

  .ai-project-card-inner {
    padding: 15px;
  }

  .project-image-container {
    width: 70px;
    height: 70px;
  }

  .project-icon {
    font-size: 1.8rem;
  }

  .project-title {
    font-size: 1rem;
  }

  .project-description {
    font-size: 0.8rem;
  }

  .tech-tag {
    padding: 3px 6px;
    font-size: 0.65rem;
  }

  .github-link {
    padding: 6px 12px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .ai-projects .section-title {
    font-size: 1.7rem;
  }

  .ai-project-card-inner {
    padding: 12px;
  }

  .project-image-container {
    width: 60px;
    height: 60px;
  }

  .project-icon {
    font-size: 1.6rem;
  }
}

/* Advanced Animation Keyframes */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Intersection Observer Animation Classes */
.animate-in {
  animation: animate-in 0.8s ease-out forwards;
}

/* Performance Optimizations */
.ai-project-card {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.ai-project-card:hover {
  will-change: transform;
}

.ai-project-card:not(:hover) {
  will-change: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .ai-projects *,
  .ai-projects *::before,
  .ai-projects *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
