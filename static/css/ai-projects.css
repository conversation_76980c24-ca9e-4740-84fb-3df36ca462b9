/* AI Projects Section - Ultra-Advanced, Futuristic UI for AI Student Portfolio */

/* Main Container with Enhanced 3D Environment */
.ai-projects {
  position: relative;
  background: #000000;
  padding: 120px 0;
  color: #ffffff;
  overflow: hidden;
  perspective: 1200px;
  transform-style: preserve-3d;
}

/* Advanced Neural Network Background with Dynamic Particles */
.neural-network-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  pointer-events: none;
  z-index: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(180, 44, 29, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 126, 95, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(180, 44, 29, 0.06) 0%, transparent 50%);
  animation: neuralPulse 8s ease-in-out infinite;
}

@keyframes neuralPulse {
  0%, 100% {
    opacity: 0.15;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.25;
    transform: scale(1.05) rotate(2deg);
  }
}

/* Floating Particles Matrix Effect */
.ai-projects::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(180, 44, 29, 0.5), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 126, 95, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(180, 44, 29, 0.6), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 126, 95, 0.5), transparent),
    radial-gradient(1px 1px at 160px 30px, rgba(180, 44, 29, 0.4), transparent);
  background-repeat: repeat;
  background-size: 200px 150px;
  animation: floatingParticles 25s linear infinite;
  z-index: 0;
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-150px) rotate(360deg);
    opacity: 0.3;
  }
}

/* Enhanced Section Header with Holographic Effects */
.ai-projects .section-header {
  position: relative;
  margin-bottom: 100px;
  z-index: 1;
  transform: translateZ(60px);
  text-align: center;
}

.ai-projects .section-title {
  position: relative;
  display: inline-block;
  font-size: 3.8rem;
  font-weight: 900;
  margin-bottom: 25px;
  background: linear-gradient(135deg, #ffffff 0%, #b42c1d 30%, #ff7e5f 70%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 3px;
  animation: titleGlow 4s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 30px rgba(180, 44, 29, 0.4));
}

@keyframes titleGlow {
  0% {
    filter: brightness(1) drop-shadow(0 0 20px rgba(180, 44, 29, 0.3));
    transform: translateY(0px);
  }
  100% {
    filter: brightness(1.2) drop-shadow(0 0 40px rgba(180, 44, 29, 0.6));
    transform: translateY(-3px);
  }
}

.ai-projects .section-title::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 5px;
  background: linear-gradient(90deg, transparent, #b42c1d, #ff7e5f, #b42c1d, transparent);
  border-radius: 3px;
  animation: accentPulse 3s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(180, 44, 29, 0.6);
}

@keyframes accentPulse {
  0%, 100% {
    width: 120px;
    opacity: 1;
    box-shadow: 0 0 20px rgba(180, 44, 29, 0.6);
  }
  50% {
    width: 160px;
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(180, 44, 29, 0.8);
  }
}

.ai-projects .section-subtitle {
  max-width: 800px;
  margin: 30px auto 0;
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.8;
  position: relative;
  animation: subtitleFloat 5s ease-in-out infinite;
  font-weight: 300;
}

@keyframes subtitleFloat {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.85;
  }
  50% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

/* Advanced Project Cards Container with Morphing Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 40px;
  position: relative;
  z-index: 1;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Ultra-Advanced 3D Project Card */
.ai-project-card {
  position: relative;
  background: linear-gradient(145deg, rgba(15, 15, 15, 0.95), rgba(25, 25, 25, 0.9));
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  height: 450px;
  border: 1px solid rgba(180, 44, 29, 0.4);
  display: flex;
  flex-direction: column;
  transform-style: preserve-3d;
  backdrop-filter: blur(10px);
  will-change: transform;
}

.ai-project-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(180, 44, 29, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(255, 126, 95, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  z-index: 1;
  pointer-events: none;
}

.ai-project-card:hover::before {
  opacity: 1;
}

.ai-project-card:hover {
  transform: translateY(-20px) rotateX(5deg) rotateY(5deg) translateZ(20px);
  box-shadow:
    0 25px 50px rgba(180, 44, 29, 0.3),
    0 10px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(180, 44, 29, 0.8);
}

/* Enhanced Project Image Container with Holographic Effect */
.project-image-container {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(45deg, rgba(180, 44, 29, 0.1), rgba(255, 126, 95, 0.1));
}

.project-image-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.8s ease;
  z-index: 2;
}

.ai-project-card:hover .project-image-container::before {
  left: 100%;
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.8s cubic-bezier(0.23, 1, 0.320, 1);
  filter: brightness(0.9) contrast(1.1);
}

.ai-project-card:hover .project-image {
  transform: scale(1.15) rotate(2deg);
  filter: brightness(1.1) contrast(1.2) saturate(1.2);
}

/* Holographic Overlay Effect */
.project-image-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(180, 44, 29, 0.2) 0%,
    transparent 25%,
    transparent 75%,
    rgba(255, 126, 95, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  z-index: 1;
  mix-blend-mode: overlay;
}

.ai-project-card:hover .project-image-container::after {
  opacity: 1;
}

/* Enhanced Project Type Badge with Glow Effect */
.project-type {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.95), rgba(255, 126, 95, 0.9));
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 700;
  z-index: 3;
  box-shadow:
    0 6px 15px rgba(180, 44, 29, 0.4),
    0 0 20px rgba(180, 44, 29, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: badgePulse 3s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% {
    box-shadow:
      0 6px 15px rgba(180, 44, 29, 0.4),
      0 0 20px rgba(180, 44, 29, 0.3);
  }
  50% {
    box-shadow:
      0 8px 20px rgba(180, 44, 29, 0.6),
      0 0 30px rgba(180, 44, 29, 0.5);
  }
}

/* Enhanced Project Content with Advanced Typography */
.project-content {
  padding: 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.project-title {
  font-size: 1.6rem;
  font-weight: 800;
  margin-bottom: 15px;
  color: #ffffff;
  line-height: 1.3;
  background: linear-gradient(135deg, #ffffff, rgba(180, 44, 29, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.ai-project-card:hover .project-title {
  background: linear-gradient(135deg, #ffffff, #b42c1d);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transform: translateY(-2px);
}

.project-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
  line-height: 1.6;
  flex-grow: 1;
  font-weight: 300;
  transition: color 0.3s ease;
}

.ai-project-card:hover .project-description {
  color: rgba(255, 255, 255, 0.95);
}

/* Advanced Tech Stack Tags with Micro-Animations */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
}

.tech-tag {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(20, 20, 20, 0.6));
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
  border: 1px solid rgba(180, 44, 29, 0.3);
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.tech-tag::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(180, 44, 29, 0.3), transparent);
  transition: left 0.6s ease;
}

.ai-project-card:hover .tech-tag {
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.3), rgba(255, 126, 95, 0.2));
  border-color: rgba(180, 44, 29, 0.6);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.2);
}

.ai-project-card:hover .tech-tag::before {
  left: 100%;
}

/* Enhanced Project Links with Advanced Hover Effects */
.project-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.github-link {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 700;
  font-size: 0.95rem;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
  padding: 12px 20px;
  border-radius: 30px;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.3), rgba(255, 126, 95, 0.2));
  border: 1px solid rgba(180, 44, 29, 0.4);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.github-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.6s ease;
}

.github-link:hover::before {
  left: 100%;
}

.github-link:hover {
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.6), rgba(255, 126, 95, 0.4));
  transform: translateY(-5px) scale(1.05);
  box-shadow:
    0 10px 25px rgba(180, 44, 29, 0.4),
    0 0 30px rgba(180, 44, 29, 0.3);
  border-color: rgba(180, 44, 29, 0.8);
}

.github-link i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.github-link:hover i {
  transform: rotate(360deg) scale(1.1);
}

/* Advanced AI Animation Effects with Neural Pathways */
.ai-glow {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.6) 0%, rgba(255, 126, 95, 0.3) 30%, transparent 70%);
  filter: blur(25px);
  opacity: 0;
  transition: all 0.8s ease;
  pointer-events: none;
  animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.ai-project-card:hover .ai-glow {
  opacity: 1;
}

.ai-glow.top-left {
  top: -75px;
  left: -75px;
}

.ai-glow.bottom-right {
  bottom: -75px;
  right: -75px;
}

/* Enhanced Neural Network Lines with Dynamic Animation */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.8s ease;
  pointer-events: none;
  background-image:
    linear-gradient(90deg, rgba(180, 44, 29, 0.2) 1px, transparent 1px),
    linear-gradient(0deg, rgba(180, 44, 29, 0.2) 1px, transparent 1px),
    linear-gradient(45deg, rgba(255, 126, 95, 0.1) 1px, transparent 1px);
  background-size: 25px 25px, 25px 25px, 35px 35px;
  animation: neuralFlow 8s linear infinite;
}

@keyframes neuralFlow {
  0% { background-position: 0 0, 0 0, 0 0; }
  100% { background-position: 25px 25px, -25px 25px, 35px 35px; }
}

.ai-project-card:hover .neural-lines {
  opacity: 0.6;
}

/* Enhanced Featured Project with Premium Styling */
.featured-project {
  grid-column: span 2;
  height: auto;
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.98), rgba(35, 35, 35, 0.95));
  border: 2px solid rgba(180, 44, 29, 0.6);
  position: relative;
}

.featured-project::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #b42c1d, #ff7e5f, #b42c1d);
  border-radius: 22px;
  z-index: -1;
  animation: featuredBorder 3s linear infinite;
}

@keyframes featuredBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.featured-project .project-image-container {
  height: 320px;
}

.featured-project .project-content {
  padding: 40px;
}

.featured-project .project-title {
  font-size: 2.2rem;
  background: linear-gradient(135deg, #ffffff, #b42c1d, #ff7e5f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.featured-project .project-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Enhanced Featured Badge with Premium Effects */
.featured-badge {
  position: absolute;
  top: 25px;
  left: 25px;
  background: linear-gradient(135deg, #b42c1d, #ff7e5f, #b42c1d);
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 800;
  z-index: 3;
  box-shadow:
    0 8px 20px rgba(180, 44, 29, 0.5),
    0 0 30px rgba(180, 44, 29, 0.4);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: featuredPulse 2s ease-in-out infinite;
}

@keyframes featuredPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 8px 20px rgba(180, 44, 29, 0.5),
      0 0 30px rgba(180, 44, 29, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 12px 25px rgba(180, 44, 29, 0.7),
      0 0 40px rgba(180, 44, 29, 0.6);
  }
}

.featured-badge i {
  font-size: 1rem;
  animation: starSpin 3s linear infinite;
}

@keyframes starSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Advanced Mobile Projects Carousel */
.mobile-projects-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 30px 0;
  display: none;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.05), transparent);
}

.carousel-container {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  perspective: 1000px;
}

.carousel-slide {
  flex: 0 0 85%;
  max-width: 380px;
  margin: 0 20px;
  transform-style: preserve-3d;
}

/* Enhanced Carousel Controls with 3D Effects */
.carousel-controls {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  gap: 30px;
}

.carousel-btn {
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.9), rgba(15, 15, 15, 0.8));
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.4);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.3), rgba(255, 126, 95, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carousel-btn:hover::before {
  opacity: 1;
}

.carousel-btn:hover {
  background: linear-gradient(145deg, rgba(180, 44, 29, 0.8), rgba(255, 126, 95, 0.6));
  transform: translateY(-5px) scale(1.1);
  box-shadow:
    0 15px 30px rgba(180, 44, 29, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(180, 44, 29, 0.8);
}

.carousel-btn i {
  font-size: 1.2rem;
  z-index: 1;
  transition: transform 0.3s ease;
}

.carousel-btn:hover i {
  transform: scale(1.2);
}

/* Enhanced Carousel Dots with Glow Effects */
.carousel-dots {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  gap: 12px;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.4s ease;
  border: 1px solid rgba(180, 44, 29, 0.3);
  position: relative;
}

.carousel-dot::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.5), rgba(255, 126, 95, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.carousel-dot.active {
  background: linear-gradient(135deg, #b42c1d, #ff7e5f);
  transform: scale(1.4);
  box-shadow: 0 0 15px rgba(180, 44, 29, 0.6);
}

.carousel-dot.active::before {
  opacity: 1;
}

/* Ultra-Advanced View All Projects Button */
.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 60px;
}

.view-all-btn {
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.9), rgba(15, 15, 15, 0.8));
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.5);
  border-radius: 40px;
  padding: 16px 40px;
  font-size: 1.1rem;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 2px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.view-all-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.6s ease;
  z-index: 2;
}

.view-all-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(135deg, #b42c1d, #ff7e5f);
  z-index: -1;
  transition: width 0.5s ease;
}

.view-all-btn:hover::before {
  left: 100%;
}

.view-all-btn:hover::after {
  width: 100%;
}

.view-all-btn:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow:
    0 20px 40px rgba(180, 44, 29, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: transparent;
}

.view-all-btn i {
  font-size: 1.3rem;
  transition: transform 0.3s ease;
  z-index: 3;
}

.view-all-btn:hover i {
  transform: rotate(360deg) scale(1.2);
}

/* Enhanced Responsive Design with Advanced Breakpoints */
@media (max-width: 1400px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 35px;
  }

  .ai-projects .section-title {
    font-size: 3.4rem;
  }
}

@media (max-width: 1200px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
  }

  .featured-project {
    grid-column: auto;
  }

  .featured-project .project-image-container {
    height: 250px;
  }

  .ai-projects .section-title {
    font-size: 3rem;
  }

  .ai-project-card {
    height: 420px;
  }
}

@media (max-width: 992px) {
  .ai-projects {
    padding: 100px 0;
  }

  .ai-projects .section-title {
    font-size: 2.6rem;
    letter-spacing: 2px;
  }

  .ai-projects .section-subtitle {
    font-size: 1.2rem;
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
  }

  .ai-project-card {
    height: 400px;
  }

  .project-content {
    padding: 25px;
  }
}

@media (max-width: 768px) {
  .projects-grid {
    display: none;
  }

  .mobile-projects-carousel {
    display: block;
  }

  .ai-projects {
    padding: 80px 0;
  }

  .ai-projects .section-header {
    margin-bottom: 60px;
  }

  .ai-projects .section-title {
    font-size: 2.2rem;
    letter-spacing: 1px;
  }

  .ai-projects .section-subtitle {
    font-size: 1.1rem;
    max-width: 90%;
  }

  .carousel-slide {
    flex: 0 0 90%;
    max-width: 320px;
  }

  .carousel-controls {
    gap: 20px;
  }

  .carousel-btn {
    width: 50px;
    height: 50px;
  }

  .view-all-btn {
    padding: 14px 30px;
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .ai-projects {
    padding: 60px 0;
  }

  .ai-projects .section-header {
    margin-bottom: 50px;
  }

  .ai-projects .section-title {
    font-size: 1.9rem;
    letter-spacing: 0.5px;
  }

  .ai-projects .section-subtitle {
    font-size: 1rem;
    max-width: 95%;
  }

  .carousel-slide {
    flex: 0 0 95%;
    max-width: 280px;
    margin: 0 10px;
  }

  .ai-project-card {
    height: 380px;
  }

  .project-content {
    padding: 20px;
  }

  .project-title {
    font-size: 1.3rem;
  }

  .project-description {
    font-size: 0.95rem;
  }

  .tech-tag {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .carousel-btn {
    width: 45px;
    height: 45px;
  }

  .view-all-btn {
    padding: 12px 25px;
    font-size: 0.9rem;
    gap: 10px;
  }

  .featured-badge {
    padding: 8px 15px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .ai-projects .section-title {
    font-size: 1.7rem;
  }

  .carousel-slide {
    flex: 0 0 98%;
    margin: 0 5px;
  }

  .ai-project-card {
    height: 360px;
  }

  .project-image-container {
    height: 180px;
  }

  .project-content {
    padding: 18px;
  }
}

/* Advanced Animation Keyframes */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Intersection Observer Animation Classes */
.animate-in {
  animation: animate-in 0.8s ease-out forwards;
}

/* Performance Optimizations */
.ai-project-card,
.tech-tag,
.github-link,
.carousel-btn,
.view-all-btn {
  will-change: transform;
}

/* Hardware Acceleration */
.ai-projects *,
.ai-projects *::before,
.ai-projects *::after {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
