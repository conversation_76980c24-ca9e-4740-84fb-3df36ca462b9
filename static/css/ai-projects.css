/* AI Projects Section - Modern, Eye-catching UI for AI Student Portfolio */

/* Main Container */
.ai-projects {
  position: relative;
  background: #000000;
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

/* Neural Network Background Effect */
.neural-network-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.07;
  pointer-events: none;
  z-index: 0;
}

/* Section Header */
.ai-projects .section-header {
  position: relative;
  margin-bottom: 60px;
  z-index: 1;
}

.ai-projects .section-title {
  position: relative;
  display: inline-block;
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 15px;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.ai-projects .section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  border-radius: 2px;
}

.ai-projects .section-subtitle {
  max-width: 700px;
  margin: 20px auto 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* Project Cards Container */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

/* AI Project Card */
.ai-project-card {
  position: relative;
  background: rgba(20, 20, 20, 0.8);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  height: 400px;
  border: 1px solid rgba(180, 44, 29, 0.3);
  display: flex;
  flex-direction: column;
}

.ai-project-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(180, 44, 29, 0.4);
  border-color: rgba(180, 44, 29, 0.6);
}

/* Project Image Container */
.project-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.215, 0.610, 0.355, 1);
}

.ai-project-card:hover .project-image {
  transform: scale(1.1);
}

/* Project Type Badge */
.project-type {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(180, 44, 29, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* Project Content */
.project-content {
  padding: 25px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #ffffff;
  line-height: 1.3;
}

.project-description {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 15px;
  line-height: 1.5;
  flex-grow: 1;
}

/* Tech Stack Tags */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.tech-tag {
  background: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.9);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(180, 44, 29, 0.3);
}

.ai-project-card:hover .tech-tag {
  background: rgba(180, 44, 29, 0.2);
  border-color: rgba(180, 44, 29, 0.5);
}

/* Project Links */
.project-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.github-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
  background: rgba(180, 44, 29, 0.2);
  border: 1px solid rgba(180, 44, 29, 0.3);
}

.github-link:hover {
  background: rgba(180, 44, 29, 0.4);
  transform: translateY(-3px);
}

.github-link i {
  font-size: 1.1rem;
}

/* AI Animation Effects */
.ai-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.4) 0%, rgba(180, 44, 29, 0) 70%);
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.ai-project-card:hover .ai-glow {
  opacity: 1;
}

.ai-glow.top-left {
  top: -50px;
  left: -50px;
}

.ai-glow.bottom-right {
  bottom: -50px;
  right: -50px;
}

/* Neural Network Lines */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  background-image: 
    linear-gradient(90deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px),
    linear-gradient(0deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.ai-project-card:hover .neural-lines {
  opacity: 0.4;
}

/* Featured Project */
.featured-project {
  grid-column: span 2;
  height: auto;
}

.featured-project .project-image-container {
  height: 300px;
}

.featured-project .project-content {
  padding: 30px;
}

.featured-project .project-title {
  font-size: 1.8rem;
}

.featured-project .project-description {
  font-size: 1.1rem;
}

.featured-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  color: white;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 5px;
}

.featured-badge i {
  font-size: 0.9rem;
}

/* Mobile Projects Carousel */
.mobile-projects-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 20px 0;
  display: none;
}

.carousel-container {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.215, 0.610, 0.355, 1.000);
}

.carousel-slide {
  flex: 0 0 90%;
  max-width: 350px;
  margin: 0 15px;
}

.carousel-controls {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
}

.carousel-btn {
  background: rgba(20, 20, 20, 0.8);
  color: #ffffff;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.carousel-btn:hover {
  background: rgba(180, 44, 29, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(180, 44, 29, 0.3);
}

.carousel-dots {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 8px;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background: #b42c1d;
  transform: scale(1.2);
}

/* View All Projects Button */
.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.view-all-btn {
  background: rgba(20, 20, 20, 0.8);
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.5);
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.view-all-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  z-index: -1;
  transition: width 0.3s ease;
}

.view-all-btn:hover::before {
  width: 100%;
}

.view-all-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.3);
  border-color: transparent;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .featured-project {
    grid-column: auto;
  }
  
  .featured-project .project-image-container {
    height: 200px;
  }
}

@media (max-width: 992px) {
  .ai-projects .section-title {
    font-size: 2.4rem;
  }
  
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .projects-grid {
    display: none;
  }
  
  .mobile-projects-carousel {
    display: block;
  }
  
  .ai-projects .section-title {
    font-size: 2rem;
  }
  
  .ai-projects .section-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .ai-projects {
    padding: 70px 0;
  }
  
  .ai-projects .section-title {
    font-size: 1.8rem;
  }
  
  .carousel-slide {
    flex: 0 0 95%;
  }
  
  .project-title {
    font-size: 1.2rem;
  }
  
  .project-description {
    font-size: 0.9rem;
  }
}
