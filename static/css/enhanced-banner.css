/* Enhanced Main Banner Styles for Large Screens */

/* Neural Network Background */
.neural-banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.6;
}

/* Enhanced Main Banner Container */
.main-banner {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* Floating Tech Icons */
.floating-tech-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.tech-icon {
  position: absolute;
  opacity: 0.5;
  filter: brightness(1.5);
  transition: all 0.3s ease;
  z-index: 2;
}

.tech-icon:hover {
  opacity: 0.8;
  filter: brightness(2);
  transform: scale(1.2);
}

.tech-icon-1 {
  top: 15%;
  left: 10%;
  animation: float-icon 8s ease-in-out infinite;
}

.tech-icon-2 {
  top: 25%;
  right: 15%;
  animation: float-icon 7s ease-in-out infinite 1s;
}

.tech-icon-3 {
  bottom: 20%;
  left: 20%;
  animation: float-icon 9s ease-in-out infinite 2s;
}

.tech-icon-4 {
  bottom: 30%;
  right: 10%;
  animation: float-icon 10s ease-in-out infinite 3s;
}

.tech-icon-5 {
  top: 50%;
  left: 5%;
  animation: float-icon 11s ease-in-out infinite 4s;
}

.tech-icon-6 {
  top: 40%;
  right: 5%;
  animation: float-icon 12s ease-in-out infinite 5s;
}

@keyframes float-icon {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(10px, -10px) rotate(5deg); }
  50% { transform: translate(0, -20px) rotate(0deg); }
  75% { transform: translate(-10px, -10px) rotate(-5deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

/* Enhanced Profile Image */
.profile-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-frame {
  position: absolute;
  width: calc(100% + 40px);
  height: calc(100% + 40px);
  border: 2px solid #b42c1d;
  border-radius: 15px;
  z-index: 1;
  animation: pulse-frame 3s ease-in-out infinite;
}

@keyframes pulse-frame {
  0% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 0.5; }
}

.profile-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.3) 0%, transparent 70%);
  filter: blur(20px);
  z-index: 0;
  animation: pulse-glow 4s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% { opacity: 0.3; }
  50% { opacity: 0.6; }
  100% { opacity: 0.3; }
}

.banner-image {
  position: relative;
  z-index: 2;
  transition: all 0.5s ease;
  box-shadow: 0 10px 30px rgba(180, 44, 29, 0.3);
}

.banner-image:hover {
  transform: scale(1.03) translateY(-10px);
  box-shadow: 0 15px 40px rgba(180, 44, 29, 0.4);
}

/* Social Media Links */
.banner-social-links {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(180, 44, 29, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 18px;
  transition: all 0.3s ease;
  border: 1px solid rgba(180, 44, 29, 0.3);
}

.social-icon:hover {
  background: #b42c1d;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

/* Enhanced Typography */
.banner-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 20px;
  line-height: 1.2;
}

.banner-subtitle {
  font-size: 1.8rem;
  font-weight: 300;
  margin-bottom: 30px;
  color: #b0b0b0;
}

.banner-text {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 30px;
  max-width: 600px;
}

/* Enhanced Call-to-Action Buttons */
.banner-buttons {
  display: flex;
  gap: 20px;
  margin-top: 30px;
}

.banner-btn {
  padding: 15px 30px;
  border-radius: 30px;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.banner-btn i {
  font-size: 18px;
}

.primary-btn {
  background: #b42c1d;
  color: #ffffff;
  border: none;
}

.primary-btn:hover {
  background: #d13525;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.3);
}

.secondary-btn {
  background: transparent;
  color: #ffffff;
  border: 2px solid #b42c1d;
}

.secondary-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #b42c1d;
  z-index: -1;
  transition: width 0.3s ease;
}

.secondary-btn:hover {
  color: #ffffff;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.3);
}

.secondary-btn:hover::before {
  width: 100%;
}

/* Decorative Elements */
.decorative-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.1), transparent 70%);
  z-index: 0;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  left: -100px;
  animation: float-circle 15s ease-in-out infinite;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  right: -50px;
  animation: float-circle 12s ease-in-out infinite reverse;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 30%;
  right: 10%;
  animation: float-circle 10s ease-in-out infinite 2s;
}

@keyframes float-circle {
  0% { transform: translate(0, 0); }
  50% { transform: translate(30px, -30px); }
  100% { transform: translate(0, 0); }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .banner-title {
    font-size: 3.5rem;
  }

  .banner-subtitle {
    font-size: 1.6rem;
  }
}

@media (max-width: 992px) {
  .main-banner {
    min-height: auto;
    padding: 100px 0;
  }

  .banner-title {
    font-size: 3rem;
  }

  .banner-subtitle {
    font-size: 1.4rem;
  }

  .tech-icon {
    display: none;
  }

  /* Adjust profile image container for medium screens */
  .profile-image-container {
    margin-top: 40px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .banner-title {
    font-size: 2.5rem;
  }

  .banner-subtitle {
    font-size: 1.2rem;
  }

  .banner-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .banner-btn {
    width: 100%;
    justify-content: center;
  }

  /* Adjust profile image container for small screens */
  .profile-image-container {
    margin-top: 50px;
    max-width: 70%;
    margin-left: auto;
    margin-right: auto;
  }

  /* Adjust profile frame and glow for small screens */
  .profile-frame {
    width: calc(100% + 20px);
    height: calc(100% + 20px);
  }
}

/* Mobile-specific adjustments */
@media (max-width: 576px) {
  .main-banner {
    padding: 80px 0 60px;
  }

  .banner-title {
    font-size: 2.2rem;
  }

  /* Optimize image for mobile */
  .profile-image-container {
    margin-top: 40px;
    max-width: 85%;
    height: auto;
    aspect-ratio: 1/1;
  }

  .banner-image {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
  }

  /* Adjust decorative elements for mobile */
  .decorative-circle {
    opacity: 0.5;
  }

  /* Ensure text content is properly spaced */
  .banner-text {
    font-size: 1rem;
    margin-bottom: 20px;
  }

  .banner-skills {
    gap: 8px;
    margin: 15px 0;
  }

  .banner-skill-badge {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  /* Adjust social links for mobile */
  .banner-social-links {
    justify-content: center;
    margin-top: 20px;
  }
}

/* Neural Network Canvas Animation */
#bannerNetworkCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
