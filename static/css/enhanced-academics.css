/* Enhanced Academic Highlights Section Styles */

.academics {
  position: relative;
  background: #000000;
  padding: 100px 0;
  overflow: hidden;
}

/* Neural Network Background */
.academics-neural-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

/* Section Title Styling */
.academics .section-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #b0b0b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  margin-bottom: 10px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
}

.academics .section-subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Academic Timeline */
.academic-timeline {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.timeline-track {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 4px;
  background: rgba(180, 44, 29, 0.3);
  transform: translateX(-50%);
}

.timeline-track::before,
.timeline-track::after {
  content: "";
  position: absolute;
  left: 50%;
  width: 20px;
  height: 20px;
  background: #b42c1d;
  border-radius: 50%;
  transform: translateX(-50%);
}

.timeline-track::before {
  top: 0;
}

.timeline-track::after {
  bottom: 0;
}

/* Timeline Items */
.timeline-item {
  position: relative;
  margin-bottom: 60px;
  width: 100%;
  display: flex;
}

.timeline-item:nth-child(odd) {
  justify-content: flex-start;
  padding-right: 50%;
}

.timeline-item:nth-child(even) {
  justify-content: flex-end;
  padding-left: 50%;
}

.timeline-content {
  position: relative;
  width: 80%;
  max-width: 450px;
}

/* Timeline Dot */
.timeline-dot {
  position: absolute;
  top: 20px;
  width: 16px;
  height: 16px;
  background: #b42c1d;
  border-radius: 50%;
  border: 3px solid rgba(0, 0, 0, 0.8);
  box-shadow: 0 0 0 4px rgba(180, 44, 29, 0.3);
  z-index: 1;
}

.timeline-item:nth-child(odd) .timeline-dot {
  right: -58px;
}

.timeline-item:nth-child(even) .timeline-dot {
  left: -58px;
}

/* Timeline Card */
.timeline-card {
  background: rgba(20, 20, 20, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(180, 44, 29, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.timeline-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #b42c1d, transparent);
}

.timeline-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(180, 44, 29, 0.4);
}

/* Timeline Arrow */
.timeline-arrow {
  position: absolute;
  top: 20px;
  width: 0;
  height: 0;
  border-style: solid;
}

.timeline-item:nth-child(odd) .timeline-arrow {
  right: -20px;
  border-width: 10px 0 10px 20px;
  border-color: transparent transparent transparent rgba(20, 20, 20, 0.7);
}

.timeline-item:nth-child(even) .timeline-arrow {
  left: -20px;
  border-width: 10px 20px 10px 0;
  border-color: transparent rgba(20, 20, 20, 0.7) transparent transparent;
}

/* Timeline Content */
.timeline-year {
  display: inline-block;
  background: rgba(180, 44, 29, 0.8);
  color: #ffffff;
  font-weight: 600;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.timeline-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
}

.timeline-subtitle {
  font-size: 1.1rem;
  color: #b42c1d;
  margin-bottom: 15px;
  font-weight: 500;
}

.timeline-description {
  color: #b0b0b0;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 15px;
}

/* Academic Stats */
.academic-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 20px;
}

.academic-stat {
  background: rgba(180, 44, 29, 0.1);
  border: 1px solid rgba(180, 44, 29, 0.3);
  border-radius: 10px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.academic-stat:hover {
  background: rgba(180, 44, 29, 0.2);
  transform: translateY(-3px);
}

.stat-icon {
  color: #b42c1d;
  font-size: 1.2rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.8rem;
  color: #b0b0b0;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

/* Academic Grid Cards */
.academic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.academic-grid-card {
  background: rgba(20, 20, 20, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(180, 44, 29, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.academic-grid-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #b42c1d, transparent);
}

.academic-grid-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(180, 44, 29, 0.4);
}

.grid-card-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

.grid-card-icon i {
  font-size: 2.5rem;
  color: #b42c1d;
  transition: all 0.3s ease;
}

.grid-card-icon::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.3), transparent 70%);
  z-index: -1;
}

.academic-grid-card:hover .grid-card-icon i {
  transform: scale(1.1);
  color: #d13525;
}

.grid-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
}

.grid-card-content {
  color: #b0b0b0;
  font-size: 1rem;
  line-height: 1.6;
  flex: 1;
}

.grid-card-highlight {
  font-size: 2rem;
  font-weight: 700;
  color: #b42c1d;
  margin: 10px 0;
}

/* Course List */
.course-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.course-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(180, 44, 29, 0.2);
}

.course-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.course-icon {
  margin-right: 10px;
  color: #b42c1d;
}

.course-name {
  color: #ffffff;
  font-weight: 500;
}

/* Academic Interests */
.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.interest-tag {
  background: rgba(180, 44, 29, 0.1);
  border: 1px solid rgba(180, 44, 29, 0.3);
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 0.9rem;
  color: #ffffff;
  transition: all 0.3s ease;
}

.interest-tag:hover {
  background: rgba(180, 44, 29, 0.3);
  transform: translateY(-3px);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .academic-timeline {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .academics .section-title {
    font-size: 3rem;
  }
  
  .academic-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .timeline-track {
    left: 30px;
  }
  
  .timeline-item {
    padding-right: 0 !important;
    padding-left: 60px !important;
    justify-content: flex-start !important;
  }
  
  .timeline-dot {
    left: 22px !important;
    right: auto !important;
  }
  
  .timeline-arrow {
    left: 40px !important;
    right: auto !important;
    border-width: 10px 20px 10px 0 !important;
    border-color: transparent rgba(20, 20, 20, 0.7) transparent transparent !important;
  }
  
  .timeline-content {
    width: 100%;
  }
  
  .academics .section-title {
    font-size: 2.5rem;
  }
  
  .academics .section-subtitle {
    font-size: 1.1rem;
  }
  
  .academic-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 576px) {
  .academics .section-title {
    font-size: 2rem;
  }
  
  .academics .section-subtitle {
    font-size: 1rem;
  }
  
  .timeline-card {
    padding: 20px;
  }
  
  .timeline-title {
    font-size: 1.3rem;
  }
  
  .timeline-subtitle {
    font-size: 1rem;
  }
  
  .academic-grid-card {
    padding: 20px;
  }
  
  .grid-card-title {
    font-size: 1.3rem;
  }
}

/* Neural Network Canvas Animation */
#academicsNetworkCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
