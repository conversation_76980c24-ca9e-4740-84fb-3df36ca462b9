/* General Styles */
body {
  font-family: 'Poppins', sans-serif;
  background-color: #000000; /* Deep black background for overall layout */
  line-height: 1.6;
  color: #ffffff; /* White text for maximum readability */
  padding-top: 60px;
}

@keyframes gradientBG {
  0% { background-position: 0% 50%; } /* Initial background position */
  50% { background-position: 100% 50%; } /* Shift background to the right */
  100% { background-position: 0% 50%; } /* Return to initial position */
}

.section {
  padding: 100px 0;
}

/* Navbar Styles */
.navbar {
  z-index: 1050;
}

/* Roll-On Animation for Name */
#animated-name {
  font-size: 2.5rem; /* Adjusted size */
  font-weight: bold;
  color: #e0e0e0; /* Light font color for dark background */
  overflow: hidden;
  white-space: nowrap;
  height: 3rem; /* Matches font size */
  position: relative;
  margin-left: 20px; /* Added space between "I'm" and the name */
}

#animated-name span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; /* Ensure the text spans across the container */
  text-align: left; /* Align text properly */
  animation: rollOn 4s linear infinite; /* Duration for two names */
  opacity: 0; /* Start hidden */
}

/* Keyframes for Roll-On Animation */
@keyframes rollOn {
  0% {
      opacity: 0;
      transform: translateY(100%);
  }
  10% {
      opacity: 1;
      transform: translateY(0);
  }
  40% {
      opacity: 1;
      transform: translateY(0);
  }
  50% {
      opacity: 0;
      transform: translateY(-100%);
  }
  100% {
      opacity: 0;
      transform: translateY(-100%);
  }
}

#animated-name span:nth-child(1) {
  animation-delay: 0s;
}
#animated-name span:nth-child(2) {
  animation-delay: 2s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  #animated-name {
      font-size: 2rem;
      height: 2.5rem;
  }
}

@media (max-width: 576px) {
  #animated-name {
      font-size: 1.8rem;
      height: 2rem;
  }
}

/* Hover Effects for Animated Name */
#animated-name:hover span {
  animation-play-state: paused;
}

#animated-name:hover {
  cursor: pointer;
}
/* Dynamic Background Utilities */
.dynamic-background {
  position: relative;
  overflow: hidden;
}

.dynamic-background::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  opacity: 0.15;
  animation: rotate 25s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Banner Section - Enhanced */
.main-banner {
  background: #000000; /* Deep black background */
  position: relative;
  overflow: hidden;
  padding: 120px 0;
  display: flex;
  align-items: center;
}

/* Modern floating accent elements */
.main-banner::before {
  content: "";
  position: absolute;
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.15), transparent 70%);
  border-radius: 50%;
  filter: blur(20px);
  animation: float 8s ease-in-out infinite;
  z-index: 0;
}

.main-banner::after {
  content: "";
  position: absolute;
  bottom: 15%;
  right: 10%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.1), transparent 70%);
  border-radius: 50%;
  filter: blur(15px);
  animation: float 6s ease-in-out infinite reverse;
  z-index: 0;
}

@keyframes float {
  0% { transform: translate(0, 0); }
  50% { transform: translate(20px, -20px); }
  100% { transform: translate(0, 0); }
}

/* Content Container */
.main-banner .container {
  position: relative;
  z-index: 2;
}

/* Text Section */
.main-banner h2,
.main-banner p {
  z-index: 2;
  position: relative; /* Ensures text is above pseudo-elements */
}

.main-banner h2 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 20px;
  color: #ffffff;
}

@keyframes gradientText {
  0% { background-position: 0% center; }
  50% { background-position: 100% center; }
  100% { background-position: 0% center; }
}

/* Image Container - Modern Design */
.banner-image-container {
  position: relative;
  background: transparent;
  border-radius: 20px;
  padding: 0;
  z-index: 3;
  isolation: isolate;
  overflow: visible;
}

/* Image Section - Modern Design */
.banner-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  object-fit: contain;
  border-radius: 20px;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: block;
  position: relative;
  z-index: 3;
  filter: drop-shadow(0 10px 20px rgba(180, 44, 29, 0.3));
  transform-style: preserve-3d;
  perspective: 1000px;
}

.banner-image:hover {
  transform: translateY(-10px) rotateY(5deg);
  filter: drop-shadow(0 15px 30px rgba(180, 44, 29, 0.4));
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-banner {
    padding: 80px 20px;
    flex-direction: column;
    align-items: center;
  }

  .banner-image-container {
    margin-top: 20px;
    width: 90%;
  }

  .banner-image {
    max-width: 100%;
  }

  .main-banner .col-lg-6 {
    text-align: left;
  }
}

@media (max-width: 576px) {
  .main-banner {
    padding: 60px 15px;
  }

  .banner-image-container {
    width: 95%;
  }

  .main-banner .btn {
    padding: 12px 25px;
    font-size: 0.9rem;
  }
}

/* Button Styling - Modern Design */
.main-banner .btn {
  background: transparent;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 30px;
  padding: 15px 35px;
  margin-top: 30px;
  margin-bottom: 20px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid #b42c1d;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.main-banner .btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #b42c1d;
  z-index: -1;
  transition: width 0.4s ease;
}

/* Button Hover Effect */
.main-banner .btn:hover {
  color: #ffffff;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.3);
}

.main-banner .btn:hover::before {
  width: 100%;
}

/* Banner Skills Section - Modern Design */
.banner-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 25px 0;
}

.banner-skill-badge {
  display: inline-block;
  background: rgba(180, 44, 29, 0.1);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  border: 1px solid #b42c1d;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.banner-skill-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #b42c1d;
  z-index: -1;
  transition: width 0.3s ease;
}

.banner-skill-badge:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
  color: #ffffff;
}

.banner-skill-badge:hover::before {
  width: 100%;
}

/* Image Section */
.banner-image {
  width: 100%;
  max-width: 600px;  /* Set a max width to prevent it from getting too large */
  height: auto;      /* Maintain aspect ratio */
  object-fit: contain; /* Ensure the image maintains its aspect ratio */
  border: none;      /* No border around the image */
  box-shadow: none;
  border-radius: 10px; /* No shadow effect */
  transition: width 0.3s ease-in-out; /* Smooth transition for resizing */
}

/* Responsive Design for Small Screens */
@media (max-width: 768px) {
  .main-banner {
    padding: 80px 20px; /* Adjust padding for smaller screens */
    flex-direction: column; /* Stack text and image vertically */
    align-items: center; /* Center align the content */
  }

  .banner-image {
    max-width: 90%;  /* Ensure the image is scaled appropriately on smaller screens */
    margin-top: 20px; /* Add some spacing between text and image */
  }

  .main-banner .col-lg-6 {
    text-align: left; /* Center the text on small screens */
  }
}

@media (max-width: 576px) {
  .main-banner {
    padding: 60px 15px; /* Adjust padding further for smaller screens */
  }

  .banner-image {
    max-width: 95%; /* Ensure image takes most of the available width */
  }

  .main-banner .btn {
    padding: 12px 25px; /* Adjust button size for smaller screens */
    font-size: 0.9rem; /* Make the button text smaller */
  }
}
/* About Section Styles */
/* About Section */
.about {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

.about::before {
  display: none; /* Remove pattern */
}

/* Section Title Styling - Applied to all sections */
.section-title {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 20px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding-bottom: 15px;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #b42c1d; /* Dull orange accent */
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.5rem;
  color: #b0b0b0;
  margin-bottom: 40px;
  font-weight: 300;
  letter-spacing: 1px;
}

/* Ensure all section titles have the same styling */
h2.section-title {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 20px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding-bottom: 15px;
}

h2.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #b42c1d; /* Dull orange accent */
  border-radius: 2px;
}

.about-card-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

/* Interactive About Card */
.about-card {
  display: flex;
  background: #b42c1d; /* Dull orange color */
  padding: 50px;
  border-radius: 25px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.5);
  width: 80%;
  max-width: 1200px;
  flex-wrap: wrap;
  justify-content: space-between;
  transition: all 0.4s ease-in-out;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.about-card:hover {
  transform: scale(1.05) rotate(1deg);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.about-image {
  flex: 1 1 300px;
  padding: 20px;
  transition: transform 0.3s ease;
}

.about-image:hover {
  transform: scale(1.05);
}

.about-info {
  flex: 2;
  padding-left: 40px;
  text-align: left;
}

.about-info h3 {
  font-size: 2.5rem;
  color: #e0e0e0;
  margin-bottom: 20px;
}

.about-info p {
  font-size: 1.4rem;
  color: #b0b0b0;
  margin-bottom: 20px;
  line-height: 1.6;
}

.about-stats {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  margin-bottom: 30px;
}

.about-stat-item {
  flex: 1 1 250px;
  display: flex;
  gap: 15px;
  align-items: center;
  font-size: 1.1rem;
  color: #c0c0c0;
  text-align: left;
}

.about-stat-item i {
  font-size: 2rem;
  color: #ff9800;
}

.about-stat-item h4 {
  font-size: 1.3rem;
  font-weight: bold;
  color: #e0e0e0;
}

.about-stat-item p {
  color: #b0b0b0;
}

/* About Section Button - Enhanced Contrast */
.about-cta {
  background: rgba(180, 44, 29, 0.15);
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 30px;
  padding: 15px 35px;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-right: 15px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid #b42c1d;
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-decoration: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.about-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #b42c1d;
  z-index: -1;
  transition: width 0.4s ease;
}

.about-cta:hover {
  color: #ffffff;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.5);
  border-color: #ff3c1d;
}

.about-cta:hover::before {
  width: 100%;
}

/* Special styling for buttons in the About card (which has orange background) */
.about-card .about-cta {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid #ffffff;
  color: #ffffff;
}

.about-card .about-cta::before {
  background: #ffffff;
}

.about-card .about-cta:hover {
  color: #b42c1d;
  border-color: #ffffff;
}

/* Modal Button Styling */
.modal-footer .about-cta {
  background: rgba(180, 44, 29, 0.2);
  border: 2px solid #b42c1d;
  margin: 0 5px;
}

.modal-footer .about-cta:hover {
  border-color: #ff3c1d;
}

/* Responsive Design */
@media (max-width: 992px) {
  .about-card {
    flex-direction: column;
    padding: 30px;
  }

  .about-info {
    padding-left: 0;
    text-align: center;
  }

  .about-stats {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 576px) {
  .about-info h3 {
    font-size: 2rem;
  }

  .about-info p {
    font-size: 1rem;
  }

  .about-stat-item h4 {
    font-size: 1.1rem;
  }

  .about-stat-item p {
    font-size: 0.9rem;
  }
}
/* Skills Section Styles */
/* Skills Section Styling */
.skills {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

.skills::before {
  display: none; /* Remove pattern */
}

/* Floating AI Glow - Removed */

@keyframes floatingGlow {
  from { transform: translateY(0px); }
  to { transform: translateY(20px); }
}

/* Skill Cards */
.skill-card {
  position: relative;
  background: #b42c1d; /* Dull orange color */
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  z-index: 1;
  margin-bottom: 40px; /* Added space between rows */
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-card:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 30px rgba(255, 0, 150, 0.3);
}

/* Skill Icons */
.skill-icon {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    margin-bottom: 15px;
  transition: all 0.3s ease;
}

.skill-card:hover .skill-icon {
  background: rgba(255, 0, 150, 0.3);
  transform: scale(1.1);
}

.skill-icon img {
  width: 100%;
  height: auto;
}

/* Skill Titles */
.skill-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #e0e0e0;
}

/* Skill Descriptions */
.skill-card p {
  font-size: 1rem;
  color: #b0b0b0;
}

/* AI Effect */
.skill-card::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100px;
  height: 100px;
    background: radial-gradient(circle, rgba(255, 0, 150, 0.3), transparent);
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0;
}

.skill-card:hover::before {
  opacity: 1;
  transform: scale(1.5);
}

/* Spacing Between Rows */
.row {
  margin-bottom: 50px; /* Space between rows */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .skill-card {
      padding: 20px;
  }

  .skill-icon {
      width: 60px;
      height: 60px;
  }

  .skill-card h4 {
      font-size: 1.2rem;
  }
}
/* Projects Section Styles */
/* Projects Section */
.projects {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff; /* White text color */
  overflow: hidden;
}

.projects::before {
  display: none; /* Remove pattern */
}

/* Keep the existing gradientBG animation if not already defined elsewhere */
@keyframes gradientBG {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes moveBG {
  0% { background-position: 0 0; }
  100% { background-position: 100px 100px; }
}

/* Add this to ensure content stays above the animated background */
.projects .container {
  position: relative;
  z-index: 1;
}

/* Update text colors for better visibility */
.projects h2,
.projects .text-center {
  color: #e0e0e0;
}

.project-item {
  position: relative;
}

.project-card {
  position: relative;
  overflow: hidden;
  height: 360px;
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: #b42c1d; /* Dull orange color */
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.2);
}

.project-image {
  width: 100%;
  height: 100%;
  display: block;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-overlay h4 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.project-overlay p {
  font-size: 0.9rem;
  margin-bottom: 15px;
  text-align: center;
}

.tech-stack .badge {
  display: inline-block;
  margin: 5px;
  padding: 5px 10px;
    background: #007bff; /* Modern blue */
  color: white;
  font-size: 0.8rem;
  border-radius: 20px;
}

.tech-stack .badge:hover {
  background: #ff6a9c;
}

.btn-outline-light {
  border: 2px solid #ffffff;
  color: #ffffff;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.btn-outline-light:hover {
  background-color: #ffffff;
  color: #ff3c83;
}
/* Modern Mobile Projects Slider */
.mobile-projects-slider {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 20px 0;
  margin-bottom: 30px;
}

.slider-container {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.215, 0.610, 0.355, 1.000);
  margin: 0 auto;
  width: 100%;
}

.slider-slide {
  flex: 0 0 85%;
  max-width: 350px;
  margin: 0 10px;
  border-radius: 15px;
  overflow: hidden;
  transform: scale(0.95);
  transition: all 0.4s cubic-bezier(0.215, 0.610, 0.355, 1.000);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.slider-slide.active {
  transform: scale(1);
  box-shadow: 0 15px 35px rgba(180, 44, 29, 0.3);
}

.slider-controls {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 15px;
}

.slider-btn {
  background: transparent;
  color: #ffffff;
  border: 2px solid #b42c1d;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.slider-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #b42c1d;
  z-index: -1;
  transition: width 0.3s ease;
  border-radius: 50%;
}

.slider-btn:hover::before {
  width: 100%;
}

.slider-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

.slider-dots {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  gap: 8px;
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: #b42c1d;
  transform: scale(1.2);
}

/* Certifications Section Styles */
.certifications {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff; /* White text color for readability */
  overflow: hidden;
}

/* Research Section Styles */
.research {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

/* Shared animation keyframes */
@keyframes gradientBG {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Enhanced animation for these sections */
.research,
.certifications {
  animation: energeticGradient 12s ease infinite;
}

@keyframes energeticGradient {
  0% {
    background-position: 0% 50%;
    background-size: 400% 400%;
  }
  25% {
    background-size: 200% 200%;
  }
  50% {
    background-position: 100% 50%;
    background-size: 300% 300%;
  }
  75% {
    background-size: 200% 200%;
  }
    100% {
    background-position: 0% 50%;
    background-size: 400% 400%;
    }
}

/* Shared pattern overlay for all sections - Removed */

/* Enhanced overlay pattern for these sections - Removed */

@keyframes energeticPattern {
  0% {
    background-position: 0 0;
    opacity: 0.05;
  }
  25% {
    opacity: 0.08;
  }
  50% {
    background-position: 100px 100px;
    opacity: 0.1;
  }
  75% {
    opacity: 0.08;
  }
  100% {
    background-position: 200px 200px;
    opacity: 0.05;
  }
}

/* Shared container styles */
.about .container,
.certifications .container,
.research .container,
.skills .container,
.projects .container {
  position: relative;
  z-index: 1;
}

/* Animation for background pattern movement */
@keyframes moveBG {
  0% { background-position: 0 0; }
  100% { background-position: 100px 100px; }
}

/* Enhanced card styles for these sections */
.research-card,
.certification-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.research-card:hover,
.certification-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 45px 0 rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

/* Text styling for research cards */
.research-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  color: white;
  margin-bottom: 15px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.research-card p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Button styling */
.research-links .btn {
  margin-top: 10px;
  background-color: rgba(108, 117, 125, 0.8);
    color: white;
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 0.9rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.research-links .btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #2d3e50;
  border-color: transparent;
  transform: translateY(-2px);
}

/* Ensure text remains readable in all states */
.research-card:hover h4,
.research-card:hover p {
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about,
  .certifications,
  .research,
  .skills,
  .projects {
    padding: 60px 0;
  }
}
/* Contact Section Styles */
/* Contact Section Styles */
/* Container Setup */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Contact Section Styling */
.contact {
  position: relative;
  background: #000000; /* Deep black background */
  overflow: hidden;
}

.contact::before {
  display: none; /* Remove pattern */
}

/* Section Title & Subtitle */
.contact .section-title {
  font-size: 2.5rem;
  color: #e0e0e0;
  margin-bottom: 10px;
  font-weight: bold;
}

.contact .section-subtitle {
  font-size: 1.1rem;
  color: #b0b0b0;
  margin-bottom: 50px;
}

/* Contact Container */
.contact-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Contact Info Box */
.contact-info {
  background: #b42c1d; /* Dull orange color */
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  width: 100%;
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Contact Info Title */
.contact-info h4 {
  font-size: 1.8rem;
  color: #e0e0e0;
  margin-bottom: 30px;
  font-weight: bold;
}

/* Contact Info Items */
.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.contact-icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

/* Link and Text Styling */
.contact-item a,
.contact-item p {
  font-size: 1rem;
  color: #b0b0b0;
  text-decoration: none;
  font-weight: 600;
}

/* Fix Email Overflow Issue */
.contact-email {
  display: inline-block;
  max-width: 100%;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  text-align: center;
}

/* Hover Effects */
.contact-item a:hover {
  color: #FF9800;
}

/* Final Note Styling */
.final-note {
  margin-top: 50px;
  font-size: 1.1rem;
  color: #b0b0b0;
  font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .contact {
    padding: 80px 0;
  }
  .contact-info {
    padding: 30px;
  }
}

@media (max-width: 576px) {
  .contact-info {
    padding: 20px;
  }
  .section-title {
    font-size: 2rem;
  }
  .section-subtitle {
    font-size: 1rem;
  }
}
/* Updated Footer Styles: Dark & Modern */
footer {
  background: #000000;          /* Deep black background */
  color: #ffffff;               /* White text for readability */
  padding: 30px 0;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1); /* Subtle top border */
}

  footer p {
  margin: 0;
  font-size: 0.95rem;
}

/* Social Links in Footer */
footer .social-links {
  margin-top: 15px;
}

footer .social-links a {
  margin: 0 10px;
  color: inherit;             /* Inherit the footer text color */
  transition: color 0.3s ease, opacity 0.3s ease;
}

footer .social-links a:hover {
  color: #888888;             /* Slightly lighter grey on hover */
  opacity: 0.8;
}


/* Floating Chat Button Styles */
.floating-chat-btn {
  position: fixed;
  right: clamp(15px, 3vw, 25px);
  bottom: clamp(15px, 3vh, 25px);
  z-index: 9999;
}

.floating-chat-btn button {
  background: #222222; /* Dark gray background */
  color: white;
  width: clamp(50px, 8vw, 60px);
  height: clamp(50px, 8vw, 60px);
  padding: clamp(10px, 2vw, 15px);
  border-radius: 50%;
  border: 2px solid #b42c1d; /* Dull orange border */
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-chat-btn button img {
  width: clamp(20px, 4vw, 24px);
  height: clamp(20px, 4vw, 24px);
  filter: brightness(0) invert(1);
  transition: transform 0.3s ease;
}

.floating-chat-btn button:hover img {
  transform: scale(1.1);
}

.particle-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(180, 44, 29, 0.3);
  animation: particleRing 2s linear infinite;
}

@keyframes particleRing {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.floating-chat-btn button:hover {
  transform: scale(1.1) rotate(10deg);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
  background: #333333; /* Slightly lighter gray on hover */
  border-color: #d45713; /* Brighter orange border on hover */
}

.floating-chat-window {
  position: fixed;
  right: clamp(15px, 3vw, 25px);
  bottom: -100%;
  width: clamp(300px, 90vw, 380px);
  height: clamp(400px, 80vh, 600px);
  background: rgba(15, 15, 20, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 9998;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-chat-window.show {
  bottom: clamp(15px, 3vh, 25px);
  animation: windowAppear 0.5s forwards;
}

@keyframes windowAppear {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.chat-header {
  padding: clamp(15px, 2vh, 20px);
  background: rgba(180, 44, 29, 0.2); /* Dull orange to match theme */
  border-radius: 20px 20px 0 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ai-indicator {
  width: 8px;
  height: 8px;
  background: #b42c1d; /* Dull orange to match theme */
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.ai-info {
  display: flex;
  flex-direction: column;
}

.typing-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.status {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

@keyframes pulse {
  0% { opacity: 0.5; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1.05); }
  100% { opacity: 0.5; transform: scale(0.95); }
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: white;
}

.ai-avatar-large {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  position: relative;
}

.avatar-ring {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(180, 44, 29, 0.5); /* Dull orange to match theme */
  animation: ringPulse 2s infinite;
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
}

.message {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.message.user {
  align-items: flex-end;
}

.message.ai {
  align-items: flex-start;
}

.user-message-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 70%;
}

.ai-message-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 70%;
}

.message-content {
  padding: 12px 16px;
  border-radius: 15px;
  font-size: 14px;
  line-height: 1.4;
}

.user .message-content {
  background: linear-gradient(135deg, #3a8ffe, #9658fe);
  color: white;
  border-bottom-right-radius: 4px;
}

.ai .message-content {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-bottom-left-radius: 4px;
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  background: #fff;  /* Add a white background to make the image more visible */
}

.ai-message-wrapper img.avatar {
  width: 28px;
  height: 28px;
  min-width: 28px;  /* Prevent the image from shrinking */
  border-radius: 50%;
  object-fit: cover;
}

.user-message-wrapper img.avatar {
  width: 28px;
  height: 28px;
  min-width: 28px;  /* Prevent the image from shrinking */
  border-radius: 50%;
  object-fit: cover;
}

.ai-avatar-large img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.chat-input-wrapper {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0 0 20px 20px;
  position: sticky;
  bottom: 0;
}

.chat-input {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 8px;
}

.chat-input textarea {
  flex: 1;
  min-height: 24px;
  max-height: 100px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: white;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
}

.send-btn-animated {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: #b42c1d; /* Dull orange to match theme */
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.send-btn-particle {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(180, 44, 29, 0.2) 70%);
  transform: scale(0);
  transition: transform 0.5s;
}

.send-btn-animated:hover .send-btn-particle {
  transform: scale(2);
}

.input-features {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.feature-dots {
  display: flex;
  gap: 5px;
}

.feature-dots span {
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.input-suggestion {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.4);
}

/* Message Animations */
.message {
  margin-bottom: 15px;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.user-message-wrapper, .ai-message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  max-width: 80%;
}

.user-message-wrapper {
  margin-left: auto;
  flex-direction: row-reverse;
}

.ai-message-wrapper {
  margin-right: auto;
}

.message-content {
  padding: 10px 15px;
  border-radius: 15px;
  word-wrap: break-word;
  max-width: 100%;
}

.user .message-content {
  background: #b42c1d; /* Dull orange to match theme */
  color: white;
}

.ai .message-content {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(180, 44, 29, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(180, 44, 29, 0); }
  100% { box-shadow: 0 0 0 0 rgba(180, 44, 29, 0); }
}

@keyframes ringPulse {
  0% { transform: scale(1); opacity: 1; border-color: rgba(180, 44, 29, 0.5); }
  50% { transform: scale(1.1); opacity: 0.5; border-color: rgba(180, 44, 29, 0.3); }
  100% { transform: scale(1); opacity: 1; border-color: rgba(180, 44, 29, 0.5); }
}
/* Header Styles */
header {
  position: relative;
  z-index: 1050;
}

.navbar {
  background: #000000;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand .logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  background: linear-gradient(to right, #a8dadc, #f4a261);
  background-clip: text; /* Standard property */
  -webkit-background-clip: text; /* For WebKit-based browsers */
  -webkit-text-fill-color: transparent;
  animation: logoAnimation 5s linear infinite;
}

@keyframes logoAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.navbar .nav-link {
  font-size: 1rem;
  font-weight: 500;
  color: #f1faee;
  transition: color 0.3s ease, transform 0.3s ease;
}

.navbar .nav-link:hover {
  color: #f4a261;
  transform: translateY(-3px);
}

.navbar .nav-link.active {
  font-weight: 700;
  color: #e63946;
}

.navbar-toggler {
  border: none;
  background: #e63946;
}

.navbar-toggler-icon {
  background-image: linear-gradient(to right, #1d3557, #457b9d);
}

.navbar.sticky {
  background: #000000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sticky Navbar on Scroll */
.sticky {
  position: fixed;
  top: 0;
  width: 100%;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .navbar .nav-link {
    font-size: 0.9rem;
  }

  .navbar-brand .logo-text {
    font-size: 1.5rem;
  }
}
/* Distinct styling for the Download Resume link */
.navbar-nav .download-resume {
background-color: #3e9157;  /* Vibrant warm orange */
color: #ffffff !important;  /* White text */
padding: 8px 15px;
border-radius: 30px;
font-weight: bold;
margin-left: 15px;
transition: background-color 0.3s ease;
border: none;
}
/* Custom Dropdown Button (Navbar Toggler) */
.navbar-toggler {
  border: none;
  background: none;
  padding: 8px;
  outline: none !important;
  box-shadow: none !important;
}

/* Custom Icon for Navbar Toggler */
.navbar-toggler-icon {
  width: 30px;
  height: 30px;
  background-image: none; /* Remove default Bootstrap icon */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

/* Custom Animated Burger Icon */
.navbar-toggler-icon::before,
.navbar-toggler-icon::after,
.navbar-toggler-icon span {
  content: "";
  display: block;
  background: #ffffff; /* White lines */
  height: 3px;
  width: 30px;
  border-radius: 3px;
  transition: all 0.3s ease-in-out;
}

/* Space between lines */
.navbar-toggler-icon span {
  margin: 5px 0;
}

/* Navbar Open Animation */
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon span {
  opacity: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::before {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::after {
  transform: rotate(-45deg) translate(5px, -5px);
}

.navbar-nav .download-resume:hover {
background-color: #b42c1d;  /* Slightly darker on hover */
color: #ffffff !important;
}
/* Academic Highlights Section Styles */
/* Academic Section Styling */
/* Academic Highlights & Certifications Section */
.academics {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff; /* White text color */
  overflow: hidden;
}

/* Remove Background Decorative Wave */
.academic-bg {
  display: none; /* Hide the decorative wave for consistency */
}

/* Academic Card Styling */
.academic-card {
  background: #b42c1d; /* Dull orange color */
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.academic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.academic-icon {
  font-size: 2rem;
  color: #ff8c00;
  margin-bottom: 10px;
}

.academic-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  color: #e0e0e0;
}

.academic-card p {
  font-size: 1.2rem;
  color: #ff7b00;
  font-weight: bold;
}

/* Certifications Integrated Section */
.certifications-section {
  background: #b42c1d; /* Dull orange color */
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.certifications-section h4 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 15px;
}

.certifications-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: space-between;
}

.cert-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  width: 48%;
  justify-content: space-between;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cert-icon {
  font-size: 1.5rem;
  color: #ff8c00;
}

.cert-name {
  font-weight: bold;
  font-size: 1rem;
  color: #ffffff;
  flex-grow: 1;
  margin-left: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .academic-card,
  .certifications-section {
      padding: 15px;
  }

  .academic-card h4,
  .certifications-section h4 {
      font-size: 1.3rem;
  }

  .cert-name {
      font-size: 0.9rem;
  }

  .certifications-list {
      flex-direction: column;
  }

  .cert-item {
      width: 100%;
  }
}
/* Research Papers Section Styles */
/* Research Papers Section Styles */
.research {
  position: relative;
  background: #000000; /* Deep black background */
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

/* Research Paper Card */
.research-card {
  background: #b42c1d; /* Dull orange color */
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5), 0 0 15px rgba(180, 44, 29, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.research-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 45px 0 rgba(0, 0, 0, 0.6);
  background: #c13a2d; /* Slightly lighter orange on hover */
}

/* Text styling for research cards */
.research-card h4 {
  font-size: 1.4rem;
  font-weight: bold;
  color: white;
  margin-bottom: 15px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.research-card p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Button styling */
.research-links .btn {
  margin-top: 10px;
  background-color: rgba(108, 117, 125, 0.8);
  color: white;
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 0.9rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.research-links .btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #2d3e50;
  border-color: transparent;
  transform: translateY(-2px);
}

/* Ensure text remains readable in all states */
.research-card:hover h4,
.research-card:hover p {
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
}

/* Responsive Adjustments for Research Section */
@media (max-width: 768px) {
  .research-card {
      padding: 20px;
  }

  .research-card h4 {
      font-size: 1.2rem;
  }
}
 .floating-chat-btn {
      position: fixed;
      right: 10px;
      bottom: 10px;
      z-index: 9999;
    }

    .floating-chat-btn button {
      background-color: #007bff;
      color: white;
      padding: 15px;
      border-radius: 50%;
      border: none;
      font-size: 24px;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transition: transform 0.3s ease;
    }

    .floating-chat-btn button:hover {
      transform: scale(1.1);
    }

    /* Chat Pointer Arrow */
    .chat-pointer {
      position: absolute;
      right: 10px;
      bottom: 60px;
      font-size: 30px;
      color: #ff9900;
      display: none;
      z-index: 9998;
      animation: pointer-animation 1s infinite;
    }

    @keyframes pointer-animation {
      0% { transform: scale(1); }
      50% { transform: scale(1.2); }
      100% { transform: scale(1); }
    }
  #projects-slider {
    position: relative;
    display: flex;
    overflow-x: hidden;
  }

  .slider-wrapper {
    display: flex;
    transition: transform 0.5s ease-in-out; /* Smooth horizontal scroll */
  }

  .slide {
    flex: 0 0 100%;  /* Ensure each slide takes full width of the container */
    padding: 10px;
  }

  .slider-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    border: none;
    padding: 10px;
    cursor: pointer;
    z-index: 10;
  }

  #prev {
    left: 10px;
  }

  #next {
    right: 10px;
  }

  /* Button hover effect */
  .slider-nav-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
/* Certificates Section */
.certificates.section {
  padding: 100px 0;
  background-color: #000000; /* Deep black background */
}

.certificates h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #ffffff;
}

.certificates p {
  font-size: 1.1rem;
  color: #ffffff;
  margin-bottom: 40px;
}

/* Carousel Indicators (Dotted Slider) */
.carousel-indicators {
  bottom: -30px;
}
.carousel-indicators button {
  background-color: #adf7e8;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin: 0 5px;
  opacity: 0.8;
  transition: background-color 0.3s ease, opacity 0.3s ease;
}
.carousel-indicators button.active,
.carousel-indicators button:hover {
  background-color: #333;
  opacity: 1;
}

/* Carousel Slide & Certificate Image Styles */
.carousel-item {
  padding: 20px 0;
}

.certificate-img {
  max-height: 400px;
    object-fit: contain;
  cursor: pointer;
  transition: transform 0.3s ease;
}
.certificate-img:hover {
  transform: scale(1.05);
}

/* Optional: Customize Carousel Controls */
.carousel-control-prev-icon,
.carousel-control-next-icon {
  filter: invert(100%);
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 50%;
}
/* Floating blog button */
.floating-blog-btn {
  position: fixed;
  bottom: 25px;
  left: 25px;
  z-index: 9999;
}

.floating-blog-btn .btn-blog {
  background: linear-gradient(135deg, #ff3c3c, #e22f71);
  color: #fff;
  padding: 10px 20px;
  border-radius: 50px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: bold;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.floating-blog-btn .btn-blog:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}
