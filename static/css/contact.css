/* Modern Contact Section Styles */
.contact {
  position: relative;
  background: #000000;
  padding: 100px 0;
  color: #ffffff;
  overflow: hidden;
}

/* Apply section title styling to contact section */
.contact .section-title {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 20px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding-bottom: 15px;
}

.contact .section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #b42c1d; /* Dull orange accent */
  border-radius: 2px;
}

.contact .section-subtitle {
  font-size: 1.5rem;
  color: #b0b0b0;
  margin-bottom: 40px;
  font-weight: 300;
  letter-spacing: 1px;
}

/* Contact Card */
.contact-card {
  background: #000000;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  position: relative;
  border: 1px solid rgba(180, 44, 29, 0.3);
  max-width: 800px;
  margin: 0 auto;
}

.contact-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.contact-card-inner {
  position: relative;
  z-index: 1;
}

/* Contact Card Header */
.contact-header {
  background: #b42c1d;
  padding: 30px;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.contact-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.8), rgba(180, 44, 29, 0.4));
  z-index: -1;
}

.contact-header h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #ffffff;
}

.contact-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Contact Body */
.contact-body {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Contact Items */
.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
  padding: 15px;
  border-radius: 12px;
  background: rgba(180, 44, 29, 0.1);
  border: 1px solid rgba(180, 44, 29, 0.2);
}

.contact-item:hover {
  transform: translateX(10px);
  background: rgba(180, 44, 29, 0.15);
}

.contact-icon-wrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(180, 44, 29, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.location-icon {
  color: #ffffff;
  font-size: 24px;
}

.contact-info {
  flex: 1;
}

.contact-info h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: #ffffff;
}

.contact-link {
  color: #b0b0b0;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.contact-link:hover {
  color: #b42c1d;
}

/* Contact Footer */
.contact-footer {
  padding: 20px 30px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
}

.contact-cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: #b42c1d;
  color: #ffffff;
  padding: 12px 30px;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.contact-cta-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #ffffff;
  z-index: -1;
  transition: width 0.3s ease;
}

.contact-cta-btn:hover {
  color: #b42c1d;
}

.contact-cta-btn:hover::before {
  width: 100%;
}

.final-note {
  text-align: center;
  margin-top: 40px;
  font-size: 1.2rem;
  color: #b0b0b0;
  font-style: italic;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .contact-header {
    padding: 25px 20px;
  }

  .contact-header h3 {
    font-size: 1.8rem;
  }

  .contact-body {
    padding: 20px;
    gap: 15px;
  }

  .contact-item {
    padding: 12px;
    gap: 15px;
  }

  .contact-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .contact-icon, .location-icon {
    width: 20px;
    height: 20px;
    font-size: 20px;
  }

  .contact-info h5 {
    font-size: 1rem;
  }

  .contact-link, .contact-info p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .contact-header h3 {
    font-size: 1.5rem;
  }

  .contact-header p {
    font-size: 0.9rem;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .contact-cta-btn {
    padding: 10px 25px;
    font-size: 0.9rem;
  }

  .final-note {
    font-size: 1rem;
  }
}
