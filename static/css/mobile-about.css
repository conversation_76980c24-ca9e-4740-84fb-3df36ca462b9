/* Mobile About Section Styles */
.about-mobile {
  padding: 3rem 0;
  background-color: #000;
}

.section-title-mobile {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #fff;
  position: relative;
  display: inline-block;
}

.section-title-mobile::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: #b42c1d;
}

.mobile-subtitle {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin-top: 1.5rem;
}

.mobile-tag {
  display: inline-block;
  padding: 5px 12px;
  background-color: #b42c1d;
  color: #fff;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.mobile-about-card {
  background-color: #111;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  margin-top: 2rem;
  padding-bottom: 1.5rem;
}

.mobile-profile-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: -60px auto 1rem;
}

.mobile-profile-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.6) 0%, rgba(0, 0, 0, 0) 70%);
  filter: blur(10px);
  z-index: 0;
}

.mobile-profile-img {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid #b42c1d;
  z-index: 1;
  box-shadow: 0 0 20px rgba(180, 44, 29, 0.5);
}

.mobile-about-content {
  padding: 0 1.5rem;
  text-align: center;
}

.mobile-about-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #fff;
}

.mobile-about-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #ccc;
  margin-bottom: 1.5rem;
}

.mobile-skills {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin-bottom: 1.5rem;
}

.mobile-skill-pill {
  display: inline-block;
  padding: 6px 12px;
  background-color: #222;
  color: #fff;
  border-radius: 20px;
  font-size: 0.8rem;
  border: 1px solid #333;
}

.mobile-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 1.5rem;
}

.mobile-btn {
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.primary-btn {
  background-color: #b42c1d;
  color: #fff;
}

.primary-btn:hover {
  background-color: #d13525;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.4);
}

.secondary-btn {
  background-color: transparent;
  color: #fff;
  border: 1px solid #b42c1d;
}

.secondary-btn:hover {
  background-color: rgba(180, 44, 29, 0.1);
  transform: translateY(-2px);
}
