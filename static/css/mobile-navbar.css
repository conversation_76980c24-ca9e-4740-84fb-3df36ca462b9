/* Mobile Navbar Enhancements */

/* Base Navbar Styles */
.navbar {
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(180, 44, 29, 0.3);
  padding: 15px 0;
  transition: all 0.4s ease;
}

/* Navbar Brand */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  position: relative;
  z-index: 1060;
}

.navbar-brand .logo-text {
  background: linear-gradient(135deg, #ffffff, #b0b0b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.navbar-brand .logo-text::after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0;
  height: 2px;
  background: #b42c1d;
  transition: width 0.3s ease;
}

.navbar-brand:hover .logo-text::after {
  width: 100%;
}

/* Navbar Toggler */
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 0;
  width: 40px;
  height: 40px;
  position: relative;
  z-index: 1060;
  outline: none !important;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Custom Toggler Icon */
.navbar-toggler .toggler-icon {
  width: 30px;
  height: 2px;
  background-color: #ffffff;
  display: block;
  position: relative;
  margin: 6px 0;
  transition: all 0.3s ease;
}

.navbar-toggler .toggler-icon::before,
.navbar-toggler .toggler-icon::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  left: 0;
  transition: all 0.3s ease;
}

.navbar-toggler .toggler-icon::before {
  top: -8px;
}

.navbar-toggler .toggler-icon::after {
  bottom: -8px;
}

/* Toggler Animation */
.navbar-toggler[aria-expanded="true"] .toggler-icon {
  background-color: transparent;
}

.navbar-toggler[aria-expanded="true"] .toggler-icon::before {
  top: 0;
  transform: rotate(45deg);
  background-color: #b42c1d;
}

.navbar-toggler[aria-expanded="true"] .toggler-icon::after {
  bottom: 0;
  transform: rotate(-45deg);
  background-color: #b42c1d;
}

/* Navbar Collapse */
.navbar-collapse {
  transition: all 0.4s ease;
}

/* Mobile Menu Styles */
@media (max-width: 991px) {
  .navbar-collapse {
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 0 0 15px 15px;
    border: 1px solid rgba(180, 44, 29, 0.2);
    border-top: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
    position: absolute;
    top: 70px;
    left: 0;
    right: 0;
    z-index: 1050;
  }
  
  .navbar-nav {
    padding: 10px 0;
  }
  
  .nav-item {
    position: relative;
    margin: 8px 0;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.4s forwards;
  }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .nav-item:nth-child(1) { animation-delay: 0.1s; }
  .nav-item:nth-child(2) { animation-delay: 0.15s; }
  .nav-item:nth-child(3) { animation-delay: 0.2s; }
  .nav-item:nth-child(4) { animation-delay: 0.25s; }
  .nav-item:nth-child(5) { animation-delay: 0.3s; }
  .nav-item:nth-child(6) { animation-delay: 0.35s; }
  .nav-item:nth-child(7) { animation-delay: 0.4s; }
  .nav-item:nth-child(8) { animation-delay: 0.45s; }
  .nav-item:nth-child(9) { animation-delay: 0.5s; }
  .nav-item:nth-child(10) { animation-delay: 0.55s; }
  
  .nav-link {
    color: #ffffff !important;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .nav-link::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: #b42c1d;
    transition: width 0.3s ease;
  }
  
  .nav-link:hover {
    background: rgba(180, 44, 29, 0.1);
    padding-left: 30px;
  }
  
  .nav-link:hover::before {
    width: 10px;
  }
  
  .nav-link.active {
    background: rgba(180, 44, 29, 0.2);
    color: #b42c1d !important;
    font-weight: 600;
  }
  
  .nav-link.active::before {
    width: 10px;
  }
  
  /* Special styling for Download Resume button */
  .download-resume {
    background: #b42c1d;
    color: #ffffff !important;
    border-radius: 30px !important;
    padding: 12px 25px !important;
    margin-top: 15px;
    text-align: center;
    font-weight: 600 !important;
    letter-spacing: 0.5px;
    box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
  }
  
  .download-resume::before {
    display: none;
  }
  
  .download-resume:hover {
    background: #d13525;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(180, 44, 29, 0.4);
    padding-left: 25px !important;
  }
  
  /* Add icons to nav links */
  .nav-link i {
    margin-right: 10px;
    font-size: 1.2rem;
    color: #b42c1d;
    transition: all 0.3s ease;
  }
  
  .nav-link:hover i {
    transform: translateX(3px);
  }
}

/* Scrolled Navbar Styles */
.navbar.scrolled {
  background-color: rgba(0, 0, 0, 0.95);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  padding: 10px 0;
}

/* Navbar Collapse Animation */
.collapsing {
  transition: height 0.3s ease;
}

/* Active Link Indicator */
.nav-item.active .nav-link {
  color: #b42c1d !important;
  font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.3rem;
  }
  
  .navbar-toggler {
    width: 35px;
    height: 35px;
  }
  
  .navbar-toggler .toggler-icon {
    width: 25px;
  }
  
  .nav-link {
    font-size: 1rem;
    padding: 10px 15px;
  }
  
  .download-resume {
    padding: 10px 20px !important;
    font-size: 0.9rem;
  }
}
