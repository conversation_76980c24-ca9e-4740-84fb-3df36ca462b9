/* Enhanced Blog Section Styles */

body {
  background-color: #000000;
  color: #ffffff;
}

/* Blog Header */
.blog-header {
  position: relative;
  background: #000000;
  color: #ffffff;
  padding: 120px 0 80px;
  text-align: center;
  margin-bottom: 60px;
  overflow: hidden;
}

.blog-header-content {
  position: relative;
  z-index: 2;
}

.blog-header h1 {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #b0b0b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.blog-header p {
  font-size: 1.2rem;
  color: #b0b0b0;
  max-width: 700px;
  margin: 0 auto;
  font-weight: 300;
  letter-spacing: 1px;
}

/* Neural Network Background */
.blog-neural-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  z-index: 1;
  pointer-events: none;
}

/* Blog Categories */
.blog-categories {
  display: flex;
  justify-content: center;
  margin: 0 auto 40px;
  flex-wrap: wrap;
  gap: 15px;
  max-width: 800px;
}

.blog-category {
  padding: 10px 20px;
  background: rgba(20, 20, 20, 0.7);
  border: 1px solid rgba(180, 44, 29, 0.3);
  border-radius: 30px;
  color: #ffffff;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.blog-category:hover {
  background: rgba(180, 44, 29, 0.2);
  transform: translateY(-3px);
}

.blog-category.active {
  background: rgba(180, 44, 29, 0.8);
  color: #ffffff;
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

/* Blog Posts */
.blog-posts {
  padding: 0 0 80px;
}

.blog-post {
  background: rgba(20, 20, 20, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(180, 44, 29, 0.2);
  margin-bottom: 40px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.blog-post::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #b42c1d, transparent);
}

.blog-post:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(180, 44, 29, 0.4);
}

.blog-post h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.blog-post:hover h2 {
  color: #b42c1d;
}

.blog-post .post-date {
  font-size: 0.9rem;
  color: #b0b0b0;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.blog-post .post-date i {
  margin-right: 8px;
  color: #b42c1d;
}

.blog-post .tags {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.blog-post .tags .badge {
  background: rgba(180, 44, 29, 0.1);
  border: 1px solid rgba(180, 44, 29, 0.3);
  color: #ffffff;
  font-size: 0.85rem;
  font-weight: 500;
  padding: 5px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.blog-post .tags .badge:hover {
  background: rgba(180, 44, 29, 0.3);
  transform: translateY(-3px);
}

.blog-post p {
  color: #b0b0b0;
  line-height: 1.7;
  font-size: 1rem;
  margin-bottom: 20px;
}

.blog-post .full-text {
  margin-top: 20px;
}

.blog-post .full-text p {
  margin-bottom: 15px;
}

.blog-post .full-text ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.blog-post .full-text li {
  margin-bottom: 8px;
  color: #b0b0b0;
}

/* Blog Post Image */
.blog-post-image {
  width: 100%;
  height: 250px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
}

.blog-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.blog-post:hover .blog-post-image img {
  transform: scale(1.05);
}

.blog-post-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 50%);
}

/* Toggle Button */
.toggle-btn {
  background: transparent;
  color: #b42c1d;
  border: 1px solid #b42c1d;
  border-radius: 30px;
  padding: 8px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.toggle-btn:hover {
  background: rgba(180, 44, 29, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.2);
}

.toggle-btn:focus {
  box-shadow: none;
}

/* Featured Post */
.featured-post {
  position: relative;
}

.featured-post::after {
  content: "Featured";
  position: absolute;
  top: 20px;
  right: -30px;
  background: #b42c1d;
  color: #ffffff;
  padding: 5px 30px;
  font-size: 0.8rem;
  font-weight: 600;
  transform: rotate(45deg);
  z-index: 1;
}

/* Blog Post Author */
.blog-post-author {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  border: 2px solid #b42c1d;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: #ffffff;
}

/* Code Blocks */
.code-block {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  overflow-x: auto;
  border-left: 3px solid #b42c1d;
}

.code-block pre {
  margin: 0;
  color: #b0b0b0;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* Blog Post Stats */
.blog-post-stats {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(180, 44, 29, 0.2);
}

.stat-item {
  display: flex;
  align-items: center;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.stat-item i {
  margin-right: 8px;
  color: #b42c1d;
}

/* Pagination */
.blog-pagination {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.page-item .page-link {
  background: rgba(20, 20, 20, 0.7);
  color: #ffffff;
  border: 1px solid rgba(180, 44, 29, 0.2);
  margin: 0 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.page-item .page-link:hover {
  background: rgba(180, 44, 29, 0.2);
  border-color: rgba(180, 44, 29, 0.4);
}

.page-item.active .page-link {
  background: rgba(180, 44, 29, 0.8);
  border-color: #b42c1d;
}

/* Footer */
footer {
  background: rgba(10, 10, 10, 0.9);
  padding: 30px 0;
  text-align: center;
  border-top: 1px solid rgba(180, 44, 29, 0.2);
}

footer p {
  color: #b0b0b0;
  font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .blog-header h1 {
    font-size: 3rem;
  }
  
  .blog-post h2 {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .blog-header {
    padding: 100px 0 60px;
  }
  
  .blog-header h1 {
    font-size: 2.5rem;
  }
  
  .blog-header p {
    font-size: 1.1rem;
  }
  
  .blog-category {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
  
  .blog-post {
    padding: 20px;
  }
  
  .blog-post h2 {
    font-size: 1.4rem;
  }
  
  .blog-post-image {
    height: 200px;
  }
}

@media (max-width: 576px) {
  .blog-header h1 {
    font-size: 2rem;
  }
  
  .blog-header p {
    font-size: 1rem;
  }
  
  .blog-post h2 {
    font-size: 1.3rem;
  }
  
  .blog-post-image {
    height: 180px;
  }
  
  .blog-post-stats {
    flex-wrap: wrap;
    gap: 15px;
  }
}

/* Neural Network Canvas Animation */
#blogNetworkCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
