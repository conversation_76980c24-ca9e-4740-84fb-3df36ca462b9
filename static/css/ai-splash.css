/* AI/ML Specific Elements for Splash Animation */

/* Neural Network Nodes */
.neural-node {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #b42c1d;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(180, 44, 29, 0.8);
  z-index: 10;
  pointer-events: none;
}

/* AI/ML Icons in Splash */
.ai-ml-icon {
  position: absolute;
  opacity: 0;
  filter: brightness(1.5);
  z-index: 10;
  pointer-events: none;
  animation: fade-in-out 3s ease-in-out infinite;
}

@keyframes fade-in-out {
  0% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 0.4; transform: scale(1); }
  100% { opacity: 0; transform: scale(0.8); }
}

/* Binary Code in Splash */
.splash-binary {
  position: absolute;
  font-family: monospace;
  font-size: 12px;
  color: rgba(180, 44, 29, 0.3);
  white-space: nowrap;
  overflow: hidden;
  z-index: 5;
  pointer-events: none;
  opacity: 0;
  animation: binary-fade 5s linear infinite;
}

@keyframes binary-fade {
  0% { opacity: 0; }
  30% { opacity: 0.4; }
  70% { opacity: 0.4; }
  100% { opacity: 0; }
}

/* Data Flow Lines in Splash */
.splash-flow-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(180, 44, 29, 0.5), transparent);
  z-index: 5;
  pointer-events: none;
  opacity: 0;
  animation: flow-animation 3s linear infinite;
}

@keyframes flow-animation {
  0% { opacity: 0; background-position: -100% 0; }
  20% { opacity: 0.5; }
  80% { opacity: 0.5; }
  100% { opacity: 0; background-position: 200% 0; }
}

/* AI Brain Outline in Splash */
.splash-brain-outline {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background-image: url('https://cdn-icons-png.flaticon.com/512/2103/2103633.png');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  z-index: 1;
  pointer-events: none;
  animation: brain-pulse 5s ease-in-out infinite;
}

@keyframes brain-pulse {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
  50% { opacity: 0.1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
}

/* Neural Network Connections */
.neural-connection {
  position: absolute;
  background: linear-gradient(90deg, rgba(180, 44, 29, 0.3), rgba(180, 44, 29, 0.1));
  height: 1px;
  transform-origin: left center;
  z-index: 5;
  pointer-events: none;
}

/* AI Code Snippet in Splash */
.splash-code {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: rgba(180, 44, 29, 0.4);
  background-color: rgba(0, 0, 0, 0.3);
  padding: 8px;
  border-radius: 5px;
  border-left: 2px solid rgba(180, 44, 29, 0.5);
  max-width: 200px;
  z-index: 5;
  pointer-events: none;
  opacity: 0;
  animation: code-fade 8s ease-in-out infinite;
}

@keyframes code-fade {
  0% { opacity: 0; transform: translateY(10px); }
  10% { opacity: 0.5; transform: translateY(0); }
  90% { opacity: 0.5; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(10px); }
}

.splash-keyword {
  color: #b42c1d;
  font-weight: bold;
}

.splash-function {
  color: #d13525;
}

.splash-comment {
  color: rgba(180, 44, 29, 0.6);
  font-style: italic;
}

/* Enhanced Particles */
.enhanced-particle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.8), rgba(180, 44, 29, 0));
  pointer-events: none;
  z-index: 5;
  animation: particle-pulse 3s ease-in-out infinite;
}

@keyframes particle-pulse {
  0% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.6; }
  100% { transform: scale(0.8); opacity: 0.3; }
}

/* AI/ML Terminology */
.ai-term {
  position: absolute;
  font-family: 'Arial', sans-serif;
  font-size: 14px;
  font-weight: bold;
  color: rgba(180, 44, 29, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 5;
  pointer-events: none;
  opacity: 0;
  animation: term-fade 6s ease-in-out infinite;
}

@keyframes term-fade {
  0% { opacity: 0; transform: scale(0.9); }
  20% { opacity: 0.4; transform: scale(1); }
  80% { opacity: 0.4; transform: scale(1); }
  100% { opacity: 0; transform: scale(0.9); }
}
