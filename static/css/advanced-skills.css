/* Advanced Skills Section Styles */

.skills {
  position: relative;
  background: #000000;
  padding: 100px 0;
  overflow: hidden;
}

/* Neural Network Background */
.skills-neural-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

/* Section Title Styling */
.skills .section-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #b0b0b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  margin-bottom: 10px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
}

.skills .section-subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Skills Categories */
.skills-categories {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 15px;
}

.skill-category {
  padding: 10px 20px;
  background: rgba(20, 20, 20, 0.7);
  border: 1px solid rgba(180, 44, 29, 0.3);
  border-radius: 30px;
  color: #ffffff;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skill-category:hover {
  background: rgba(180, 44, 29, 0.2);
  transform: translateY(-3px);
}

.skill-category.active {
  background: rgba(180, 44, 29, 0.8);
  color: #ffffff;
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

/* Skills Grid */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

/* Advanced Skill Card */
.advanced-skill-card {
  background: rgba(20, 20, 20, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(180, 44, 29, 0.2);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.advanced-skill-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #b42c1d, transparent);
}

.advanced-skill-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(180, 44, 29, 0.4);
}

.skill-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.skill-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  position: relative;
}

.skill-icon img {
  width: 100%;
  height: auto;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.skill-icon::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.3), transparent 70%);
  z-index: -1;
}

.advanced-skill-card:hover .skill-icon img {
  transform: scale(1.1);
  filter: brightness(0) invert(1) drop-shadow(0 0 5px rgba(180, 44, 29, 0.8));
}

.skill-title {
  flex: 1;
}

.skill-title h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: #ffffff;
}

.skill-level {
  font-size: 0.9rem;
  color: #b42c1d;
  font-weight: 600;
}

.skill-description {
  color: #b0b0b0;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

/* Skill Tags */
.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: auto;
}

.skill-tag {
  padding: 5px 12px;
  background: rgba(180, 44, 29, 0.1);
  border: 1px solid rgba(180, 44, 29, 0.3);
  border-radius: 20px;
  font-size: 0.85rem;
  color: #ffffff;
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: rgba(180, 44, 29, 0.3);
  transform: translateY(-3px);
}

/* Progress Bar */
.skill-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 15px 0;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #b42c1d, #d13525);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Skill Mastery Indicator */
.skill-mastery {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.mastery-level {
  display: flex;
  align-items: center;
}

.mastery-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.mastery-dot.filled {
  background: #b42c1d;
}

.mastery-dot.empty {
  background: rgba(255, 255, 255, 0.1);
}

.mastery-text {
  font-size: 0.8rem;
  color: #b0b0b0;
}

/* Skill Category Badges */
.skill-category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 10px;
  background: rgba(180, 44, 29, 0.2);
  border-radius: 15px;
  font-size: 0.8rem;
  color: #ffffff;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .skills .section-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .skills-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .skills .section-title {
    font-size: 2.5rem;
  }
  
  .skills .section-subtitle {
    font-size: 1.1rem;
  }
  
  .skill-category {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .skills .section-title {
    font-size: 2rem;
  }
  
  .skills .section-subtitle {
    font-size: 1rem;
  }
  
  .advanced-skill-card {
    padding: 20px;
  }
  
  .skill-icon {
    width: 50px;
    height: 50px;
  }
  
  .skill-title h3 {
    font-size: 1.3rem;
  }
}

/* Neural Network Canvas Animation */
#skillsNetworkCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
