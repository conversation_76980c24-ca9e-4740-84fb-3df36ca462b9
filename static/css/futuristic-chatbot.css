/* Futuristic AI Chatbot UI - Uncommon, Modern Design with Wow Effects */

:root {
  --primary-color: #b42c1d;
  --primary-glow: rgba(180, 44, 29, 0.6);
  --secondary-color: #ff7e5f;
  --dark-bg: #121212;
  --darker-bg: #0a0a0a;
  --light-text: #ffffff;
  --dim-text: rgba(255, 255, 255, 0.7);
  --glass-bg: rgba(30, 30, 30, 0.6);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-highlight: rgba(255, 255, 255, 0.05);
  --neon-glow: 0 0 10px var(--primary-glow), 0 0 20px var(--primary-glow);
}

/* Floating Chat Button - Holographic Design */
.floating-chat-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1060;
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Animated Floating Message */
.floating-chat-message {
  position: absolute;
  bottom: 70px;
  right: 0;
  width: 260px;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--primary-color);
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  animation: messageFloat 5s ease-in-out infinite;
  z-index: 1050; /* Same z-index as chat interface */
}

.floating-chat-message.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.floating-chat-message .message-content {
  padding: 12px 15px;
  color: var(--light-text);
  font-size: 0.9rem;
  line-height: 1.4;
}

.floating-chat-message .message-content p {
  margin: 0;
  animation: textFade 0.5s ease forwards;
  animation-delay: 1s;
  opacity: 0;
}

.floating-chat-message .message-arrow {
  position: absolute;
  bottom: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--primary-color);
}

/* Typing Indicator in Message */
.floating-chat-message .typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  height: 20px;
}

.floating-chat-message .typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--primary-color);
  display: inline-block;
  animation: typingBounce 1.4s infinite ease-in-out;
}

.floating-chat-message .typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.floating-chat-message .typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.floating-chat-message .typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.holographic-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: transparent;
  border: 2px solid var(--primary-color);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.holographic-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(180, 44, 29, 0.1),
    rgba(180, 44, 29, 0.3),
    rgba(180, 44, 29, 0.1),
    transparent
  );
  animation: rotate 4s linear infinite;
}

.holographic-button::after {
  content: "";
  position: absolute;
  inset: 3px;
  background: var(--darker-bg);
  border-radius: 50%;
  z-index: 1;
}

.holographic-button .button-content {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.holographic-button .ai-icon {
  width: 28px;
  height: 28px;
  position: relative;
}

.holographic-button .ai-icon::before,
.holographic-button .ai-icon::after {
  content: "";
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.8;
}

.holographic-button .ai-icon::before {
  width: 28px;
  height: 28px;
  animation: pulse 2s infinite;
}

.holographic-button .ai-icon::after {
  width: 12px;
  height: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--secondary-color);
  box-shadow: 0 0 8px var(--primary-glow);
}

.holographic-button .ai-icon .ai-rings {
  position: absolute;
  inset: -4px;
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  opacity: 0.5;
}

.holographic-button .ai-icon .ai-rings:nth-child(1) {
  animation: expand 2s infinite;
}

.holographic-button .ai-icon .ai-rings:nth-child(2) {
  animation: expand 2s infinite 0.5s;
}

.holographic-button .ai-icon .ai-rings:nth-child(3) {
  animation: expand 2s infinite 1s;
}

.holographic-button:hover {
  transform: translateY(-5px) rotateX(10deg);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.4);
}

.holographic-button:hover::before {
  animation: rotate 2s linear infinite;
}

/* Futuristic Chat Interface */
.neural-chat-interface {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 380px;
  height: 550px;
  background: var(--darker-bg);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  z-index: 1080; /* Higher z-index to ensure it's above the message */
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  pointer-events: none;
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.neural-chat-interface.active {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: all;
}

/* No need for CSS selectors to hide the message - we'll handle it in JavaScript */

/* Neural Network Background */
.neural-network-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  z-index: 0;
  overflow: hidden;
}

/* Chat Header */
.neural-chat-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--glass-bg);
  border-bottom: 1px solid var(--glass-border);
  position: relative;
  z-index: 1;
}

.neural-chat-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.neural-model-icon {
  width: 36px;
  height: 36px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.neural-model-icon::before {
  content: "";
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0.2;
}

.neural-model-icon::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  animation: pulse 2s infinite;
}

.neural-model-info {
  display: flex;
  flex-direction: column;
}

.neural-model-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--light-text);
  letter-spacing: 0.5px;
}

.neural-model-status {
  font-size: 0.8rem;
  color: var(--dim-text);
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  position: relative;
}

.status-indicator::before {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: transparent;
  border: 1px solid var(--primary-color);
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.neural-chat-controls {
  display: flex;
  gap: 10px;
}

.neural-control-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--glass-highlight);
  border: 1px solid var(--glass-border);
  color: var(--light-text);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.neural-control-btn:hover {
  background: var(--glass-bg);
  transform: translateY(-2px);
}

.neural-control-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
}

/* Chat Body */
.neural-chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  z-index: 1;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--darker-bg);
}

.neural-chat-body::-webkit-scrollbar {
  width: 6px;
}

.neural-chat-body::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

.neural-chat-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 20px;
}

/* Welcome Message */
.neural-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 30px 20px;
  gap: 15px;
}

.neural-welcome-icon {
  width: 80px;
  height: 80px;
  position: relative;
  margin-bottom: 10px;
}

.neural-welcome-icon::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 24px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0.2;
}

.neural-welcome-icon::after {
  content: "";
  position: absolute;
  width: 50px;
  height: 50px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  animation: pulse 3s infinite;
}

.neural-welcome-icon .neural-icon-rings {
  position: absolute;
  inset: -8px;
  border: 1px solid var(--primary-color);
  border-radius: 30px;
  opacity: 0.3;
}

.neural-welcome-icon .neural-icon-rings:nth-child(1) {
  animation: expand 3s infinite;
}

.neural-welcome-icon .neural-icon-rings:nth-child(2) {
  animation: expand 3s infinite 1s;
}

.neural-welcome-icon .neural-icon-rings:nth-child(3) {
  animation: expand 3s infinite 2s;
}

.neural-welcome-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--light-text);
  margin-bottom: 5px;
  background: linear-gradient(90deg, var(--light-text), var(--dim-text));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.neural-welcome-subtitle {
  font-size: 0.9rem;
  color: var(--dim-text);
  max-width: 280px;
  line-height: 1.5;
}

.neural-welcome-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

.neural-suggestion {
  padding: 8px 15px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  font-size: 0.85rem;
  color: var(--light-text);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.neural-suggestion:hover {
  background: var(--glass-highlight);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-color);
}

/* Chat Messages */
.neural-message {
  display: flex;
  gap: 12px;
  max-width: 100%;
  animation: fadeIn 0.5s ease forwards;
  position: relative;
}

.neural-message.user {
  flex-direction: row-reverse;
}

.neural-avatar {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.neural-avatar.user {
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
}

.neural-avatar.ai {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.neural-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.neural-avatar::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), transparent);
  z-index: 1;
}

.neural-message-content {
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 80%;
  position: relative;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--light-text);
}

.neural-message.user .neural-message-content {
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  border-top-right-radius: 0;
  margin-right: 8px;
}

.neural-message.ai .neural-message-content {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-top-left-radius: 0;
  margin-left: 8px;
}

.neural-message.ai .neural-message-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.05), transparent);
  border-radius: inherit;
  pointer-events: none;
}

/* Markdown Styling */
.neural-message-content strong {
  font-weight: 700;
  color: var(--primary-color);
}

.neural-message-content em {
  font-style: italic;
  opacity: 0.9;
}

.neural-message-content code {
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.2);
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 0.9em;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.neural-message-content a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px dashed var(--primary-color);
  transition: all 0.2s ease;
}

.neural-message-content a:hover {
  border-bottom: 1px solid var(--primary-color);
  opacity: 0.8;
}

.neural-message-content h3 {
  font-size: 1.2em;
  font-weight: 700;
  margin: 10px 0 5px;
  color: var(--primary-color);
}

.neural-message-content h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 8px 0 4px;
  color: var(--primary-color);
}

.neural-message-content ul {
  padding-left: 20px;
  margin: 5px 0;
}

.neural-message-content li {
  margin-bottom: 3px;
  list-style-type: disc;
}

/* Typing Indicator */
.neural-typing {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  width: fit-content;
  margin-left: 48px;
  position: relative;
}

.neural-typing::before {
  content: "";
  position: absolute;
  top: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-right: 8px solid var(--glass-border);
  border-bottom: 8px solid transparent;
}

.neural-typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  opacity: 0.7;
}

.neural-typing-dot:nth-child(1) {
  animation: bounce 1.4s infinite 0.2s;
}

.neural-typing-dot:nth-child(2) {
  animation: bounce 1.4s infinite 0.4s;
}

.neural-typing-dot:nth-child(3) {
  animation: bounce 1.4s infinite 0.6s;
}

/* Chat Input */
.neural-chat-input {
  padding: 15px 20px;
  background: var(--glass-bg);
  border-top: 1px solid var(--glass-border);
  position: relative;
  z-index: 1;
}

.neural-input-container {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  background: var(--darker-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.neural-input-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-glow);
}

.neural-input-field {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--light-text);
  font-size: 0.95rem;
  resize: none;
  max-height: 120px;
  min-height: 24px;
  padding: 0;
  outline: none;
}

.neural-input-field::placeholder {
  color: var(--dim-text);
}

.neural-send-btn {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.neural-send-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

.neural-send-btn:hover::before {
  left: 100%;
}

.neural-send-btn svg {
  width: 18px;
  height: 18px;
  fill: var(--light-text);
  transform: translateX(1px);
}

.neural-input-features {
  display: flex;
  justify-content: space-between;
  padding: 8px 5px 0;
  font-size: 0.8rem;
  color: var(--dim-text);
}

.neural-input-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.neural-input-actions {
  display: flex;
  gap: 15px;
}

.neural-action-btn {
  color: var(--dim-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.neural-action-btn:hover {
  color: var(--light-text);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes expand {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes messageFloat {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  90% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes textFade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes typingBounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .neural-chat-interface {
    width: 100%;
    height: 80vh;
    bottom: 0;
    right: 0;
    border-radius: 20px 20px 0 0;
  }

  .neural-message-content {
    max-width: 85%;
  }

  .neural-welcome-suggestions {
    flex-direction: column;
    align-items: center;
  }

  .neural-suggestion {
    width: 100%;
    text-align: center;
  }

  /* Floating Message Adjustments for Mobile */
  .floating-chat-message {
    width: 220px;
    bottom: 75px;
    right: -10px;
  }

  .floating-chat-message .message-content {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .floating-chat-btn {
    bottom: 20px;
    right: 20px;
  }
}

@media (max-width: 768px) and (min-width: 577px) {
  .neural-chat-interface {
    width: 90%;
    right: 5%;
  }

  /* Floating Message Adjustments for Tablet */
  .floating-chat-message {
    width: 240px;
  }
}
