/* AI/ML Decorative Elements for Banner */

/* Binary Code Background */
.binary-code {
  position: absolute;
  font-family: monospace;
  font-size: 14px;
  color: rgba(180, 44, 29, 0.2);
  white-space: nowrap;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.binary-line-1 {
  top: 10%;
  left: 5%;
  transform: rotate(-15deg);
  animation: binary-flow 30s linear infinite;
}

.binary-line-2 {
  bottom: 15%;
  right: 5%;
  transform: rotate(15deg);
  animation: binary-flow 25s linear infinite reverse;
}

@keyframes binary-flow {
  0% { transform: translateX(0) rotate(-15deg); }
  100% { transform: translateX(-50%) rotate(-15deg); }
}

/* Neural Network Layers */
.neural-layer {
  position: absolute;
  width: 200px;
  height: 400px;
  z-index: 1;
  pointer-events: none;
}

.layer-1 {
  top: 20%;
  left: 0;
  transform: translateX(-50%);
  opacity: 0.3;
}

.layer-2 {
  top: 20%;
  right: 0;
  transform: translateX(50%);
  opacity: 0.3;
}

.neuron {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #b42c1d;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(180, 44, 29, 0.8);
}

/* AI Brain Outline */
.ai-brain-outline {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 80%;
  background-image: url('https://cdn-icons-png.flaticon.com/512/2103/2103633.png');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

/* Data Flow Lines */
.data-flow {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.flow-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(180, 44, 29, 0.5), transparent);
  animation: flow-animation 3s linear infinite;
}

.flow-line-1 {
  width: 30%;
  top: 30%;
  left: 10%;
  transform: rotate(25deg);
  animation-delay: 0s;
}

.flow-line-2 {
  width: 25%;
  top: 45%;
  left: 20%;
  transform: rotate(-15deg);
  animation-delay: 1s;
}

.flow-line-3 {
  width: 35%;
  top: 60%;
  right: 15%;
  transform: rotate(20deg);
  animation-delay: 0.5s;
}

.flow-line-4 {
  width: 20%;
  top: 25%;
  right: 25%;
  transform: rotate(-30deg);
  animation-delay: 1.5s;
}

@keyframes flow-animation {
  0% { background-position: -100% 0; }
  100% { background-position: 200% 0; }
}

/* AI Code Snippets */
.code-snippet {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: rgba(180, 44, 29, 0.3);
  background-color: rgba(0, 0, 0, 0.3);
  padding: 10px;
  border-radius: 5px;
  border-left: 3px solid rgba(180, 44, 29, 0.5);
  max-width: 250px;
  z-index: 1;
  pointer-events: none;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.code-snippet-1 {
  top: 15%;
  left: 5%;
  transform: rotate(-5deg);
}

.code-snippet-2 {
  bottom: 20%;
  right: 5%;
  transform: rotate(5deg);
}

.keyword {
  color: #b42c1d;
  font-weight: bold;
}

.function {
  color: #d13525;
}

.comment {
  color: rgba(180, 44, 29, 0.5);
  font-style: italic;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .binary-code, .neural-layer, .ai-brain-outline, .data-flow, .code-snippet {
    display: none;
  }
}
